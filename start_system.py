#!/usr/bin/env python3
"""
AI资讯智能体系统启动脚本
用于启动后端API服务、Celery工作进程和前端开发服务器
"""

import os
import sys
import subprocess
import time
import signal
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

# 进程列表，用于清理
processes = []

def signal_handler(sig, frame):
    """信号处理器，用于优雅关闭所有进程"""
    print("\n正在关闭所有服务...")
    for process in processes:
        if process.poll() is None:  # 进程仍在运行
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    print("所有服务已关闭")
    sys.exit(0)

def check_dependencies():
    """检查必要的依赖"""
    print("检查系统依赖...")

    # 检查Python依赖
    missing_deps = []
    required_packages = [
        ('fastapi', 'FastAPI'),
        ('celery', 'Celery'),
        ('redis', 'Redis'),
        ('uvicorn', 'Uvicorn')
    ]

    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✓ {name} 已安装")
        except ImportError:
            print(f"✗ {name} 未安装")
            missing_deps.append(package)

    if missing_deps:
        print(f"\n缺失依赖: {', '.join(missing_deps)}")
        print("请运行以下命令安装依赖:")
        print("  python install_dependencies.py")
        print("或者:")
        print("  pip install -r backend/requirements-minimal.txt")
        return False
    
    # 检查Node.js
    try:
        result = subprocess.run(["node", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Node.js版本: {result.stdout.strip()}")
        else:
            print("✗ Node.js未安装")
            return False
    except FileNotFoundError:
        print("✗ Node.js未安装")
        return False
    
    # 检查Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✓ Redis连接正常")
    except Exception as e:
        print(f"✗ Redis连接失败: {e}")
        print("请确保Redis服务正在运行")
        return False
    
    return True

def start_backend():
    """启动后端API服务"""
    print("启动后端API服务...")
    os.chdir(BACKEND_DIR)
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        "PYTHONPATH": str(BACKEND_DIR),
        "ENVIRONMENT": "development"
    })
    
    # 启动FastAPI服务
    process = subprocess.Popen([
        sys.executable, "-m", "uvicorn", 
        "app.main:app", 
        "--host", "0.0.0.0", 
        "--port", "8000", 
        "--reload"
    ], env=env)
    
    processes.append(process)
    print("✓ 后端API服务已启动 (http://localhost:8000)")
    return process

def start_celery_worker():
    """启动Celery工作进程"""
    print("启动Celery工作进程...")
    os.chdir(BACKEND_DIR)
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        "PYTHONPATH": str(BACKEND_DIR),
        "ENVIRONMENT": "development"
    })
    
    # 启动Celery worker
    process = subprocess.Popen([
        sys.executable, "-m", "celery", 
        "-A", "app.core.celery_app", 
        "worker", 
        "--loglevel=info",
        "--concurrency=2"
    ], env=env)
    
    processes.append(process)
    print("✓ Celery工作进程已启动")
    return process

def start_celery_beat():
    """启动Celery定时任务调度器"""
    print("启动Celery定时任务调度器...")
    os.chdir(BACKEND_DIR)
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        "PYTHONPATH": str(BACKEND_DIR),
        "ENVIRONMENT": "development"
    })
    
    # 启动Celery beat
    process = subprocess.Popen([
        sys.executable, "-m", "celery", 
        "-A", "app.core.celery_app", 
        "beat", 
        "--loglevel=info"
    ], env=env)
    
    processes.append(process)
    print("✓ Celery定时任务调度器已启动")
    return process

def start_frontend():
    """启动前端开发服务器"""
    print("启动前端开发服务器...")
    os.chdir(FRONTEND_DIR)
    
    # 检查是否已安装依赖
    if not (FRONTEND_DIR / "node_modules").exists():
        print("安装前端依赖...")
        subprocess.run(["npm", "install"], check=True)
    
    # 启动开发服务器
    process = subprocess.Popen([
        "npm", "run", "dev"
    ])
    
    processes.append(process)
    print("✓ 前端开发服务器已启动 (http://localhost:3000)")
    return process

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("=" * 50)
    print("AI资讯智能体系统启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    print("\n开始启动各项服务...")
    
    try:
        # 启动后端服务
        start_backend()
        time.sleep(3)  # 等待后端启动
        
        # 启动Celery服务
        start_celery_worker()
        time.sleep(2)
        
        start_celery_beat()
        time.sleep(2)
        
        # 启动前端服务
        start_frontend()
        time.sleep(3)
        
        print("\n" + "=" * 50)
        print("所有服务启动完成！")
        print("=" * 50)
        print("前端地址: http://localhost:3000")
        print("后端API: http://localhost:8000")
        print("API文档: http://localhost:8000/docs")
        print("=" * 50)
        print("按 Ctrl+C 停止所有服务")
        print("=" * 50)
        
        # 保持主进程运行
        while True:
            time.sleep(1)
            # 检查进程是否还在运行
            for i, process in enumerate(processes):
                if process.poll() is not None:
                    print(f"进程 {i} 意外退出，退出码: {process.returncode}")
                    signal_handler(None, None)
    
    except KeyboardInterrupt:
        signal_handler(None, None)
    except Exception as e:
        print(f"启动过程中发生错误: {e}")
        signal_handler(None, None)

if __name__ == "__main__":
    main()
