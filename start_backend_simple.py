#!/usr/bin/env python3
"""
简单启动后端服务
"""

import subprocess
import sys
import os
import time

def check_dependencies():
    """检查依赖"""
    print("检查Python依赖...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'redis', 'sqlalchemy', 
        'psycopg2-binary', 'pymongo', 'celery'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def start_backend():
    """启动后端"""
    print("🚀 启动后端服务...")
    
    # 检查后端目录
    if not os.path.exists("backend"):
        print("❌ 后端目录不存在")
        return False
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'PYTHONPATH': os.path.abspath('backend'),
        'REDIS_URL': 'redis://default:000001@localhost:6379/0',
        'DATABASE_URL': 'postgresql://postgres:123456@localhost:5432/ai_news_agent',
        'MONGODB_URL': 'mongodb://localhost:27017/ai_news_agent',
        'MINIO_ENDPOINT': 'localhost:9000',
        'MINIO_ACCESS_KEY': 'minioadmin',
        'MINIO_SECRET_KEY': 'minioadmin'
    })
    
    try:
        # 启动FastAPI服务
        print("启动FastAPI服务在端口8000...")
        
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--reload"
        ]
        
        process = subprocess.Popen(
            cmd,
            cwd="backend",
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8'
        )
        
        # 等待服务启动
        print("等待服务启动...")
        time.sleep(3)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 后端服务启动成功!")
            print("📍 API地址: http://localhost:8000")
            print("📍 API文档: http://localhost:8000/docs")
            print("\n按 Ctrl+C 停止服务")
            
            # 持续显示输出
            try:
                for line in process.stdout:
                    print(line.strip())
            except KeyboardInterrupt:
                print("\n正在停止服务...")
                process.terminate()
                process.wait()
                print("✅ 服务已停止")
                
        else:
            print("❌ 后端服务启动失败")
            output = process.stdout.read()
            if output:
                print("错误输出:")
                print(output)
            return False
            
    except Exception as e:
        print(f"❌ 启动后端时出错: {str(e)}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 后端启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺少的依赖包")
        return
    
    # 启动后端
    start_backend()

if __name__ == "__main__":
    main()
