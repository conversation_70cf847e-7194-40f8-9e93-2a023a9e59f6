#!/usr/bin/env python3
"""
快速修复依赖问题的脚本
"""

import subprocess
import sys
from pathlib import Path

def install_pydantic_settings():
    """安装pydantic-settings"""
    print("安装pydantic-settings...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "pydantic-settings"
        ], check=True)
        print("✓ pydantic-settings安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ pydantic-settings安装失败: {e}")
        return False

def test_imports():
    """测试关键模块导入"""
    print("\n测试模块导入...")
    
    # 测试pydantic-settings
    try:
        from pydantic_settings import BaseSettings
        print("✓ pydantic_settings.BaseSettings 导入成功")
    except ImportError:
        print("❌ pydantic_settings.BaseSettings 导入失败")
        return False
    
    # 测试其他关键模块
    modules = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('celery', 'Celery'),
        ('redis', 'Redis'),
    ]
    
    all_success = True
    for module, name in modules:
        try:
            __import__(module)
            print(f"✓ {name} 导入成功")
        except ImportError:
            print(f"❌ {name} 导入失败")
            all_success = False
    
    return all_success

def test_config():
    """测试配置文件"""
    print("\n测试配置文件...")
    try:
        # 切换到backend目录
        import os
        backend_dir = Path(__file__).parent / "backend"
        os.chdir(backend_dir)
        
        # 添加到Python路径
        sys.path.insert(0, str(backend_dir))
        
        from app.core.config import settings
        print("✓ 配置文件加载成功")
        print(f"  应用名称: {settings.app_name}")
        print(f"  Redis主机: {settings.redis_host}")
        print(f"  Redis端口: {settings.redis_port}")
        return True
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False

def main():
    """主函数"""
    print("依赖修复脚本")
    print("=" * 40)
    
    # 安装缺失的依赖
    if not install_pydantic_settings():
        print("依赖安装失败，请手动安装:")
        print("pip install pydantic-settings")
        sys.exit(1)
    
    # 测试导入
    if not test_imports():
        print("模块导入测试失败")
        sys.exit(1)
    
    # 测试配置
    if not test_config():
        print("配置测试失败")
        sys.exit(1)
    
    print("\n" + "=" * 40)
    print("✅ 所有依赖修复完成!")
    print("现在可以运行: python start_system.py")

if __name__ == "__main__":
    main()
