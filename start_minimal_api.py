#!/usr/bin/env python3
"""
最小化API启动器 - 不依赖数据库
仅启动核心API功能用于测试
"""

import os
import sys
import subprocess
from pathlib import Path

# 获取项目路径
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"

def create_minimal_main():
    """创建最小化的main.py文件"""
    minimal_main_content = '''"""
最小化FastAPI应用 - 用于测试
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# 创建FastAPI应用
app = FastAPI(
    title="AI资讯智能体API",
    description="AI驱动的资讯聚合与智能改写系统",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """根路径"""
    return {"message": "AI资讯智能体API", "status": "running"}

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "AI资讯智能体API",
        "version": "1.0.0"
    }

@app.get("/api/v1/status")
async def api_status():
    """API状态"""
    return {
        "api_version": "v1",
        "status": "active",
        "features": [
            "健康检查",
            "基础API",
            "CORS支持"
        ]
    }

@app.get("/api/v1/test")
async def test_endpoint():
    """测试端点"""
    return {
        "message": "API测试成功",
        "timestamp": "2025-01-03",
        "data": {
            "test_passed": True,
            "api_working": True
        }
    }

# 错误处理
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return JSONResponse(
        status_code=404,
        content={"message": "接口不存在", "path": str(request.url)}
    )

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={"message": "服务器内部错误", "error": str(exc)}
    )
'''
    
    # 保存到临时文件
    temp_main_path = BACKEND_DIR / "minimal_main.py"
    with open(temp_main_path, 'w', encoding='utf-8') as f:
        f.write(minimal_main_content)
    
    return temp_main_path

def main():
    print("🚀 最小化API启动器")
    print("=" * 40)
    print("这个版本不依赖数据库，仅用于测试API基础功能")
    print()
    
    # 创建最小化main文件
    temp_main = create_minimal_main()
    print(f"✓ 创建临时启动文件: {temp_main}")
    
    # 切换到backend目录
    os.chdir(BACKEND_DIR)
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        "PYTHONPATH": str(BACKEND_DIR),
        "ENVIRONMENT": "development"
    })
    
    print("启动最小化API服务器...")
    print("访问地址:")
    print("  🏠 主页: http://localhost:8000")
    print("  📱 API文档: http://localhost:8000/docs")
    print("  🔍 健康检查: http://localhost:8000/health")
    print("  🧪 测试接口: http://localhost:8000/api/v1/test")
    print()
    print("按 Ctrl+C 停止服务")
    print("-" * 40)
    
    try:
        # 启动FastAPI应用（使用临时的minimal_main.py）
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "minimal_main:app", 
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], env=env, check=True)
    except KeyboardInterrupt:
        print("\n✓ 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
    finally:
        # 清理临时文件
        if temp_main.exists():
            temp_main.unlink()
            print("✓ 清理临时文件")

if __name__ == "__main__":
    main()
