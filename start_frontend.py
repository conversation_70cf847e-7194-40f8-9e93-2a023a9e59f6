#!/usr/bin/env python3
"""
前端启动脚本
"""

import subprocess
import sys
import os
import time

def run_command(command, cwd=None):
    """运行命令"""
    try:
        print(f"执行命令: {command}")
        if cwd:
            print(f"工作目录: {cwd}")
        
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.stdout:
            print("输出:", result.stdout)
        if result.stderr:
            print("错误:", result.stderr)
            
        return result.returncode == 0
    except Exception as e:
        print(f"命令执行失败: {str(e)}")
        return False

def check_node():
    """检查Node.js是否安装"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Node.js 版本: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Node.js 未安装或不在PATH中")
    return False

def check_npm():
    """检查npm是否可用"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ npm 版本: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ npm 未安装或不在PATH中")
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 前端启动器")
    print("=" * 50)
    
    # 检查前端目录
    frontend_dir = "frontend"
    if not os.path.exists(frontend_dir):
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return
    
    # 检查Node.js和npm
    if not check_node() or not check_npm():
        print("\n请先安装Node.js: https://nodejs.org/")
        return
    
    # 检查package.json
    package_json = os.path.join(frontend_dir, "package.json")
    if not os.path.exists(package_json):
        print(f"❌ package.json 不存在: {package_json}")
        return
    
    print(f"✓ 前端目录存在: {frontend_dir}")
    
    # 检查node_modules
    node_modules = os.path.join(frontend_dir, "node_modules")
    if not os.path.exists(node_modules):
        print("安装前端依赖...")
        if not run_command("npm install", cwd=frontend_dir):
            print("❌ 依赖安装失败")
            return
    else:
        print("✓ 前端依赖已安装")
    
    # 启动开发服务器
    print("\n启动前端开发服务器...")
    print("访问地址: http://localhost:5173")
    print("按 Ctrl+C 停止服务")
    print("-" * 50)
    
    try:
        # 使用npm run dev启动开发服务器
        subprocess.run(
            ["npm", "run", "dev"],
            cwd=frontend_dir,
            check=True
        )
    except KeyboardInterrupt:
        print("\n前端服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 前端启动失败: {e}")
    except Exception as e:
        print(f"❌ 启动过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
