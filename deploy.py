#!/usr/bin/env python3
"""
AI资讯智能体系统部署脚本
用于生产环境部署（不使用Docker）
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

def run_command(command, cwd=None, check=True):
    """运行命令并处理错误"""
    print(f"执行命令: {' '.join(command) if isinstance(command, list) else command}")
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=isinstance(command, str)
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            sys.exit(1)
        return e

def check_system_requirements():
    """检查系统要求"""
    print("检查系统要求...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"✗ Python版本过低: {python_version.major}.{python_version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    print(f"✓ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查Node.js
    try:
        result = run_command(["node", "--version"], check=False)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ Node.js版本: {version}")
        else:
            print("✗ Node.js未安装")
            return False
    except FileNotFoundError:
        print("✗ Node.js未安装")
        return False
    
    # 检查npm
    try:
        result = run_command(["npm", "--version"], check=False)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✓ npm版本: {version}")
        else:
            print("✗ npm未安装")
            return False
    except FileNotFoundError:
        print("✗ npm未安装")
        return False
    
    return True

def setup_backend():
    """设置后端环境"""
    print("\n设置后端环境...")
    
    # 创建虚拟环境（如果不存在）
    venv_path = BACKEND_DIR / "venv"
    if not venv_path.exists():
        print("创建Python虚拟环境...")
        run_command([sys.executable, "-m", "venv", "venv"], cwd=BACKEND_DIR)
    
    # 确定Python可执行文件路径
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:  # Unix/Linux/macOS
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"
    
    # 升级pip
    print("升级pip...")
    run_command([str(pip_exe), "install", "--upgrade", "pip"], cwd=BACKEND_DIR)
    
    # 安装依赖
    print("安装Python依赖...")
    run_command([str(pip_exe), "install", "-r", "requirements.txt"], cwd=BACKEND_DIR)
    
    # 复制环境配置文件
    env_file = BACKEND_DIR / ".env"
    env_example = BACKEND_DIR / ".env.example"
    if not env_file.exists() and env_example.exists():
        print("复制环境配置文件...")
        shutil.copy2(env_example, env_file)
        print("请编辑 backend/.env 文件配置数据库连接等信息")
    
    print("✓ 后端环境设置完成")
    return python_exe

def setup_frontend():
    """设置前端环境"""
    print("\n设置前端环境...")
    
    # 安装依赖
    print("安装前端依赖...")
    run_command(["npm", "install"], cwd=FRONTEND_DIR)
    
    # 构建生产版本
    print("构建前端生产版本...")
    run_command(["npm", "run", "build"], cwd=FRONTEND_DIR)
    
    print("✓ 前端环境设置完成")

def setup_database():
    """设置数据库"""
    print("\n设置数据库...")
    
    # 这里可以添加数据库初始化逻辑
    # 例如运行Alembic迁移
    print("请手动执行以下步骤:")
    print("1. 确保PostgreSQL和MongoDB服务正在运行")
    print("2. 创建数据库和用户")
    print("3. 运行数据库迁移: cd backend && alembic upgrade head")
    
def create_systemd_services():
    """创建systemd服务文件（Linux）"""
    if os.name != 'posix':
        print("systemd服务仅支持Linux系统")
        return
    
    print("\n创建systemd服务文件...")
    
    # 获取当前用户和项目路径
    user = os.getenv('USER', 'www-data')
    project_path = str(PROJECT_ROOT.absolute())
    python_exe = PROJECT_ROOT / "backend" / "venv" / "bin" / "python"
    
    # API服务
    api_service = f"""[Unit]
Description=AI News Agent API
After=network.target

[Service]
Type=simple
User={user}
WorkingDirectory={project_path}/backend
Environment=PATH={project_path}/backend/venv/bin
ExecStart={python_exe} -m uvicorn app.main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
"""
    
    # Celery Worker服务
    worker_service = f"""[Unit]
Description=AI News Agent Celery Worker
After=network.target

[Service]
Type=simple
User={user}
WorkingDirectory={project_path}/backend
Environment=PATH={project_path}/backend/venv/bin
ExecStart={python_exe} -m celery -A app.core.celery_app worker --loglevel=info
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
"""
    
    # Celery Beat服务
    beat_service = f"""[Unit]
Description=AI News Agent Celery Beat
After=network.target

[Service]
Type=simple
User={user}
WorkingDirectory={project_path}/backend
Environment=PATH={project_path}/backend/venv/bin
ExecStart={python_exe} -m celery -A app.core.celery_app beat --loglevel=info
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
"""
    
    # 写入服务文件
    services_dir = Path("/tmp/ai-news-agent-services")
    services_dir.mkdir(exist_ok=True)
    
    (services_dir / "ai-news-agent-api.service").write_text(api_service)
    (services_dir / "ai-news-agent-worker.service").write_text(worker_service)
    (services_dir / "ai-news-agent-beat.service").write_text(beat_service)
    
    print(f"systemd服务文件已创建在: {services_dir}")
    print("请以root权限执行以下命令安装服务:")
    print(f"sudo cp {services_dir}/*.service /etc/systemd/system/")
    print("sudo systemctl daemon-reload")
    print("sudo systemctl enable ai-news-agent-api ai-news-agent-worker ai-news-agent-beat")
    print("sudo systemctl start ai-news-agent-api ai-news-agent-worker ai-news-agent-beat")

def create_nginx_config():
    """创建Nginx配置文件"""
    print("\n创建Nginx配置...")
    
    nginx_config = """server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名
    
    # 前端静态文件
    location / {
        root /path/to/ai-news-agent/frontend/dist;  # 替换为实际路径
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # 文档页面
    location /docs {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
"""
    
    config_path = Path("/tmp/ai-news-agent-nginx.conf")
    config_path.write_text(nginx_config)
    
    print(f"Nginx配置文件已创建: {config_path}")
    print("请根据实际情况修改配置文件中的路径和域名")
    print("然后复制到Nginx配置目录: sudo cp /tmp/ai-news-agent-nginx.conf /etc/nginx/sites-available/")
    print("启用站点: sudo ln -s /etc/nginx/sites-available/ai-news-agent-nginx.conf /etc/nginx/sites-enabled/")
    print("重载Nginx: sudo nginx -t && sudo systemctl reload nginx")

def main():
    """主函数"""
    print("=" * 60)
    print("AI资讯智能体系统部署脚本")
    print("=" * 60)
    
    # 检查系统要求
    if not check_system_requirements():
        print("系统要求检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    # 设置后端
    python_exe = setup_backend()
    
    # 设置前端
    setup_frontend()
    
    # 数据库设置提示
    setup_database()
    
    # 创建服务文件
    create_systemd_services()
    
    # 创建Nginx配置
    create_nginx_config()
    
    print("\n" + "=" * 60)
    print("部署准备完成！")
    print("=" * 60)
    print("接下来的步骤:")
    print("1. 配置数据库连接 (编辑 backend/.env)")
    print("2. 运行数据库迁移")
    print("3. 安装并配置systemd服务")
    print("4. 配置Nginx (可选)")
    print("5. 启动服务")
    print("=" * 60)

if __name__ == "__main__":
    main()
