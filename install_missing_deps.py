#!/usr/bin/env python3
"""
安装缺失的数据库依赖
"""

import subprocess
import sys

def install_package(package_name, description=""):
    """安装Python包"""
    print(f"安装 {package_name}{'(' + description + ')' if description else ''}...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], check=True)
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("安装缺失的数据库依赖")
    print("=" * 40)
    
    # 需要安装的包
    packages = [
        ("motor", "MongoDB异步驱动"),
        ("pymongo", "MongoDB同步驱动"),
        ("sqlalchemy", "SQL数据库ORM"),
        ("alembic", "数据库迁移工具"),
        ("psycopg2-binary", "PostgreSQL驱动"),
        ("minio", "MinIO对象存储客户端"),
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package, description in packages:
        if install_package(package, description):
            success_count += 1
        print()  # 空行分隔
    
    print("=" * 40)
    print(f"安装完成: {success_count}/{total_count} 个包安装成功")
    
    if success_count == total_count:
        print("✅ 所有依赖安装成功!")
        print("现在可以运行: python start_backend_only.py")
    else:
        print("⚠️  部分依赖安装失败，但核心功能应该可以工作")
        print("可以尝试运行: python start_minimal_api.py")

if __name__ == "__main__":
    main()
