#!/usr/bin/env python3
"""
测试API连接
"""

import requests
import json

def test_api():
    """测试API连接"""
    print("🔍 测试API连接...")

    base_url = "http://localhost:8000"

    # 测试基本连接
    try:
        print("1. 测试根路径...")
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 根路径连接成功")
            print(f"   响应: {response.json()}")
        else:
            print("   ❌ 根路径连接失败")
    except Exception as e:
        print(f"   ❌ 根路径连接错误: {str(e)}")

    # 测试健康检查
    try:
        print("2. 测试健康检查...")
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 健康检查成功")
            print(f"   响应: {response.json()}")
        else:
            print("   ❌ 健康检查失败")
    except Exception as e:
        print(f"   ❌ 健康检查错误: {str(e)}")

    # 测试登录API（使用正确的路径）
    try:
        print("3. 测试登录API...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        # 使用正确的API路径：/api/v1/auth/login
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json=login_data,
            timeout=5
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 登录API成功")
            result = response.json()
            print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("   ❌ 登录API失败")
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   ❌ 登录API错误: {str(e)}")

    # 测试API文档
    try:
        print("4. 测试API文档...")
        response = requests.get(f"{base_url}/api/docs", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ API文档可访问")
            print(f"   📍 访问地址: {base_url}/api/docs")
        else:
            print("   ❌ API文档不可访问")
    except Exception as e:
        print(f"   ❌ API文档错误: {str(e)}")

    # 测试前端代理路径
    try:
        print("5. 测试前端代理路径...")
        response = requests.get(f"{base_url}/api/v1/auth/me", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 前端代理路径正常")
        else:
            print("   ⚠️ 前端代理路径需要认证")
    except Exception as e:
        print(f"   ❌ 前端代理路径错误: {str(e)}")

def check_services():
    """检查相关服务"""
    print("\n🔍 检查相关服务...")
    
    services = [
        ("Redis", "localhost", 6379),
        ("PostgreSQL", "localhost", 5432),
        ("MongoDB", "localhost", 27017),
        ("MinIO", "localhost", 9000)
    ]
    
    for service_name, host, port in services:
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ {service_name} ({host}:{port}) - 运行中")
            else:
                print(f"   ❌ {service_name} ({host}:{port}) - 未运行")
        except Exception as e:
            print(f"   ❌ {service_name} ({host}:{port}) - 检查失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - API测试工具")
    print("=" * 50)
    
    check_services()
    test_api()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("如果API测试失败，请:")
    print("1. 运行 'python start_backend_simple.py' 启动后端")
    print("2. 确保Redis等服务正在运行")
    print("3. 检查防火墙设置")

if __name__ == "__main__":
    main()
