#!/usr/bin/env python3
"""
快速修复缺失依赖
"""

import subprocess
import sys

def install_package(package):
    """安装Python包"""
    print(f"安装 {package}...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], check=True)
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("快速修复依赖问题")
    print("=" * 30)
    
    # 安装email-validator
    if install_package("email-validator"):
        print("\n✅ 依赖修复完成!")
        print("现在可以运行: python start_backend_only.py")
    else:
        print("\n❌ 依赖安装失败")
        print("请手动运行: pip install email-validator")

if __name__ == "__main__":
    main()
