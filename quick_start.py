#!/usr/bin/env python3
"""
快速启动脚本 - 仅启动核心API
"""

import os
import sys
import subprocess
from pathlib import Path

# 获取项目路径
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"

def main():
    print("🚀 快速启动AI资讯智能体API")
    print("=" * 40)
    
    # 切换到backend目录
    os.chdir(BACKEND_DIR)
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        "PYTHONPATH": str(BACKEND_DIR),
        "ENVIRONMENT": "development"
    })
    
    print("启动FastAPI服务器...")
    print("访问地址: http://localhost:8000/docs")
    print("按 Ctrl+C 停止服务")
    print("-" * 40)
    
    try:
        # 启动FastAPI应用
        subprocess.run([
            sys.executable, "-m", "uvicorn", 
            "app.main:app", 
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], env=env, check=True)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
