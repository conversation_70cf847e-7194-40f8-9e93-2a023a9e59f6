# 安装指南

## 环境要求

- **Python 3.8+** （必需）
- **Node.js 16+** （前端功能需要）
- **Redis** （任务队列需要）

## 快速安装

### 方法一：完整安装（推荐）

如果您已安装Node.js：
```bash
# 运行完整安装脚本
python install_dependencies.py
```

### 方法二：仅后端安装

如果您没有Node.js或只需要后端功能：
```bash
# 运行后端安装脚本
python install_backend_only.py
```

### 方法三：手动安装

#### 1. 安装最小依赖
```bash
cd backend
pip install -r requirements-minimal.txt
```

#### 2. 如果遇到smolagents版本问题
```bash
# 安装最新版本的smolagents
pip install smolagents --upgrade

# 或者安装特定版本
pip install smolagents==1.19.0
```

#### 3. 安装前端依赖
```bash
cd frontend
npm install
```

## Node.js安装指南

### Windows系统
1. 访问 [Node.js官网](https://nodejs.org/)
2. 下载LTS版本（推荐18.x或20.x）
3. 运行安装程序，按默认设置安装
4. 打开命令提示符，验证安装：
```bash
node --version
npm --version
```

### macOS系统
```bash
# 使用Homebrew安装
brew install node

# 或下载官方安装包
# 访问 https://nodejs.org/ 下载.pkg文件
```

### Linux系统
```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs
```

## 常见问题解决

### 1. Node.js未安装

如果遇到 `npm未安装` 错误：
```bash
# 先安装Node.js（见上面的安装指南）
# 然后重新运行安装脚本
python install_dependencies.py

# 或者只安装后端
python install_backend_only.py
```

### 2. smolagents版本问题

如果遇到 `ERROR: No matching distribution found for smolagents==0.3.0`，请使用以下命令：

```bash
# 查看可用版本
pip index versions smolagents

# 安装最新版本
pip install smolagents --upgrade

# 或安装指定版本（根据上面查看的可用版本）
pip install smolagents==1.19.0
```

### 2. torch安装问题

如果torch安装缓慢或失败：

```bash
# 安装CPU版本的torch（更小更快）
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu

# 或者跳过torch，系统仍可运行基本功能
pip install --no-deps transformers
```

### 3. 数据库依赖问题

如果不需要完整的数据库功能，可以跳过数据库依赖：

```bash
# 只安装核心Web功能
pip install fastapi uvicorn pydantic redis celery
```

### 4. 网络问题

如果下载缓慢，可以使用国内镜像：

```bash
# 使用清华镜像
pip install -r requirements-minimal.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 使用阿里云镜像  
pip install -r requirements-minimal.txt -i https://mirrors.aliyun.com/pypi/simple/
```

## 最小化安装

如果只想快速体验系统，可以只安装核心依赖：

```bash
# 安装最基础的依赖
pip install fastapi uvicorn redis celery python-dotenv

# 安装AI相关（可选）
pip install smolagents transformers

# 安装前端
cd frontend && npm install
```

## 验证安装

运行以下命令验证安装：

```bash
# 检查Python依赖
python -c "import fastapi, celery, redis; print('Python依赖OK')"

# 检查前端依赖
cd frontend && npm list --depth=0

# 启动系统测试
python start_system.py
```

## 环境配置

1. 复制环境配置文件：
```bash
cp backend/.env.example backend/.env
```

2. 编辑配置文件（可选，使用默认配置也能运行）：
```bash
# 编辑数据库连接等配置
nano backend/.env
```

## 启动系统

```bash
# 确保Redis正在运行
redis-server

# 启动完整系统
python start_system.py
```

访问：
- 前端：http://localhost:3000
- 后端API：http://localhost:8000
- API文档：http://localhost:8000/docs

## 故障排除

### 如果启动失败：

1. **检查端口占用**：
```bash
# 检查8000端口
netstat -an | grep 8000
# 检查3000端口  
netstat -an | grep 3000
```

2. **检查Redis**：
```bash
redis-cli ping
```

3. **查看详细错误**：
```bash
# 单独启动后端查看错误
cd backend
uvicorn app.main:app --reload
```

4. **重置环境**：
```bash
# 清理Python缓存
find . -name "*.pyc" -delete
find . -name "__pycache__" -delete

# 重新安装依赖
pip install --force-reinstall -r requirements-minimal.txt
```

## 开发模式

如果要进行开发，建议安装完整依赖：

```bash
# 安装开发依赖
pip install -r requirements.txt

# 安装代码格式化工具
pip install black isort flake8

# 格式化代码
black backend/
isort backend/
```
