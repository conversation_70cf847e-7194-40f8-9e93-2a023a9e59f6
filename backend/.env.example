# 应用配置
APP_NAME=AI资讯智能体
APP_VERSION=1.0.0
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-secret-key-here-change-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
# PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/ai_news_agent
POSTGRES_USER=ai_news_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_DB=ai_news_agent
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# MongoDB
MONGODB_URL=mongodb://localhost:27017/ai_news_agent
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DB=ai_news_agent

# Redis配置
REDIS_URL=redis://default:000001@localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_USERNAME=default
REDIS_PASSWORD=000001

# Celery配置
CELERY_BROKER_URL=redis://default:000001@localhost:6379/1
CELERY_RESULT_BACKEND=redis://default:000001@localhost:6379/2

# MinIO对象存储
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET_NAME=ai-news-agent
MINIO_SECURE=false

# AI模型配置
HUGGINGFACE_API_TOKEN=your_huggingface_token_here
DEFAULT_MODEL=microsoft/DialoGPT-medium
OPENAI_API_KEY=your_openai_api_key_here

# 新闻源配置
NEWS_SOURCES_CONFIG_PATH=config/news_sources.json
MAX_ARTICLES_PER_SOURCE=50
SCRAPING_INTERVAL_HOURS=2

# 内容处理配置
CONTENT_QUALITY_THRESHOLD=0.7
MAX_CONTENT_LENGTH=5000
MIN_CONTENT_LENGTH=100

# 发布平台配置
# 微信公众号
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 微博
WEIBO_APP_KEY=your_weibo_app_key
WEIBO_APP_SECRET=your_weibo_app_secret
WEIBO_ACCESS_TOKEN=your_weibo_access_token

# 今日头条
TOUTIAO_APP_ID=your_toutiao_app_id
TOUTIAO_APP_SECRET=your_toutiao_app_secret

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 安全配置
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=1440
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# 限流配置
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# 文件上传配置
MAX_UPLOAD_SIZE=10MB
ALLOWED_FILE_TYPES=["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"]

# 邮件配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_TLS=true

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# 开发配置
RELOAD=true
WORKERS=1
