"""
最小可工作的FastAPI后端
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import os

# 创建FastAPI应用
app = FastAPI(
    title="AI资讯智能体API",
    description="AI驱动的资讯聚合与智能改写系统",
    version="1.0.0",
    openapi_url="/api/v1/openapi.json",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_id: str
    username: str

class UserInfo(BaseModel):
    user_id: str
    username: str
    email: str = "<EMAIL>"
    is_active: bool = True

# 根路径
@app.get("/")
async def root():
    return {
        "message": "AI资讯智能体API",
        "app_name": "AI资讯智能体",
        "version": "1.0.0",
        "docs_url": "/api/docs",
        "api_url": "/api/v1",
        "status": "running"
    }

# 健康检查
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "app_name": "AI资讯智能体",
        "version": "1.0.0"
    }

# 登录API
@app.post("/api/v1/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    if request.username == "admin" and request.password == "admin123":
        return LoginResponse(
            access_token="demo_token_12345",
            user_id="user_001",
            username=request.username
        )
    raise HTTPException(status_code=401, detail="用户名或密码错误")

# 登出API
@app.post("/api/v1/auth/logout")
async def logout():
    """用户登出"""
    return {"message": "登出成功"}

# 获取用户信息
@app.get("/api/v1/auth/me", response_model=UserInfo)
async def get_current_user():
    """获取当前用户信息"""
    return UserInfo(
        user_id="user_001",
        username="admin",
        email="<EMAIL>"
    )

# 用户列表API
@app.get("/api/v1/users/")
async def get_users():
    """获取用户列表"""
    return [
        {
            "id": "user_001",
            "username": "admin",
            "email": "<EMAIL>",
            "is_active": True
        }
    ]

# 静态文件服务 - 修复路径
frontend_dist = os.path.join(os.path.dirname(__file__), "..", "frontend", "dist")
if os.path.exists(frontend_dist):
    print(f"✅ 找到前端构建文件: {frontend_dist}")
    # 先注册API路由，再注册静态文件
    app.mount("/static", StaticFiles(directory=frontend_dist), name="static")
    # 最后注册根路径的静态文件服务
    @app.get("/app")
    async def serve_frontend():
        """服务前端应用"""
        from fastapi.responses import FileResponse
        index_file = os.path.join(frontend_dist, "index.html")
        if os.path.exists(index_file):
            return FileResponse(index_file)
        else:
            return {"error": "Frontend not built", "message": "Please run 'npm run build' in frontend directory"}
else:
    print(f"⚠️ 前端构建文件不存在: {frontend_dist}")
    print("请运行: cd frontend && npm run build")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
