"""
资讯爬取智能体
"""
import asyncio
import aiohttp
from typing import Any, Dict, List, Optional
from bs4 import BeautifulSoup
from smolagents import WebSearchTool
from app.agents.base_agent import BaseAgent
from app.core.config import settings
import re
import json
from datetime import datetime


class NewsScrapingAgent(BaseAgent):
    """资讯爬取智能体"""
    
    def __init__(self):
        super().__init__(
            name="NewsScrapingAgent",
            description="负责从各种资讯源爬取AI相关新闻和文章"
        )
        
        # 添加网络搜索工具
        self.web_search_tool = WebSearchTool()
        self.agent.tools.append(self.web_search_tool)
        
        # 预定义的资讯源配置
        self.default_sources = {
            "机器之心": {
                "url": "https://www.jiqizhixin.com/",
                "selectors": {
                    "title": ".article-title",
                    "content": ".article-content",
                    "link": "a[href*='/articles/']",
                    "time": ".time"
                }
            },
            "量子位": {
                "url": "https://www.qbitai.com/",
                "selectors": {
                    "title": ".post-title",
                    "content": ".post-content",
                    "link": "a[href*='/post/']",
                    "time": ".post-time"
                }
            },
            "GitHub热门": {
                "url": "https://github.com/trending",
                "selectors": {
                    "title": "h1.h3",
                    "content": ".repository-description",
                    "link": "h1.h3 a",
                    "time": ".text-gray"
                }
            }
        }
    
    def get_required_fields(self) -> List[str]:
        """获取必需的输入字段"""
        return ["source_config"]
    
    async def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行爬取任务"""
        source_config = task.get("source_config")
        max_articles = task.get("max_articles", 10)
        keywords = task.get("keywords", ["AI", "人工智能", "机器学习", "深度学习"])
        
        if isinstance(source_config, str):
            # 如果是预定义源名称
            if source_config in self.default_sources:
                source_config = self.default_sources[source_config]
            else:
                raise ValueError(f"未知的资讯源: {source_config}")
        
        self.log_info(f"开始爬取资讯源: {source_config.get('url')}")
        
        articles = []
        
        try:
            # 使用智能体进行网页分析和内容提取
            analysis_prompt = f"""
            请分析以下网站并提取AI相关的新闻文章:
            网站URL: {source_config['url']}
            
            需要提取的信息:
            1. 文章标题
            2. 文章链接
            3. 文章摘要或简介
            4. 发布时间
            5. 作者信息(如果有)
            
            关键词过滤: {', '.join(keywords)}
            最大文章数: {max_articles}
            
            请返回JSON格式的结果。
            """
            
            # 使用智能体分析网页
            agent_result = await self._run_agent_analysis(analysis_prompt, source_config['url'])
            
            if agent_result:
                articles.extend(agent_result)
            
            # 如果智能体分析失败，使用传统爬虫方法
            if not articles:
                self.log_warning("智能体分析失败，使用传统爬虫方法")
                articles = await self._traditional_scraping(source_config, max_articles, keywords)
            
            # 对文章进行质量评估和过滤
            filtered_articles = await self._filter_and_score_articles(articles, keywords)
            
            self.log_info(f"成功爬取 {len(filtered_articles)} 篇文章")
            
            return {
                "source_url": source_config['url'],
                "articles": filtered_articles,
                "total_count": len(filtered_articles),
                "crawl_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"爬取失败: {str(e)}")
            raise
    
    async def _run_agent_analysis(self, prompt: str, url: str) -> List[Dict]:
        """使用智能体分析网页"""
        try:
            # 使用网络搜索工具获取网页内容
            search_result = self.web_search_tool.forward(f"site:{url} AI 人工智能")
            
            # 构建分析提示
            analysis_prompt = f"""
            {prompt}
            
            搜索结果:
            {search_result}
            
            请分析这些结果并提取相关的AI新闻文章信息。
            """
            
            # 使用智能体进行分析
            result = self.agent.run(analysis_prompt)
            
            # 尝试解析JSON结果
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    # 如果不是JSON，尝试从文本中提取信息
                    return self._extract_articles_from_text(result)
            
            return result if isinstance(result, list) else []
            
        except Exception as e:
            self.log_error(f"智能体分析失败: {str(e)}")
            return []
    
    async def _traditional_scraping(self, source_config: Dict, max_articles: int, keywords: List[str]) -> List[Dict]:
        """传统爬虫方法"""
        articles = []
        
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    'User-Agent': settings.user_agent,
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                }
                
                async with session.get(source_config['url'], headers=headers) as response:
                    if response.status == 200:
                        html = await response.text()
                        soup = BeautifulSoup(html, 'html.parser')
                        
                        # 提取文章链接
                        link_selector = source_config['selectors']['link']
                        links = soup.select(link_selector)[:max_articles]
                        
                        for link in links:
                            try:
                                article = await self._extract_article_info(session, link, source_config)
                                if article and self._contains_keywords(article['title'], keywords):
                                    articles.append(article)
                                    
                                # 添加延迟避免被封
                                await asyncio.sleep(settings.request_delay)
                                
                            except Exception as e:
                                self.log_warning(f"提取文章失败: {str(e)}")
                                continue
                    
        except Exception as e:
            self.log_error(f"传统爬虫失败: {str(e)}")
        
        return articles
    
    async def _extract_article_info(self, session: aiohttp.ClientSession, link_element, source_config: Dict) -> Optional[Dict]:
        """提取单篇文章信息"""
        try:
            href = link_element.get('href')
            if not href:
                return None
            
            # 处理相对链接
            if href.startswith('/'):
                base_url = source_config['url'].rstrip('/')
                href = base_url + href
            elif not href.startswith('http'):
                return None
            
            # 获取文章页面
            async with session.get(href) as response:
                if response.status != 200:
                    return None
                
                html = await response.text()
                soup = BeautifulSoup(html, 'html.parser')
                
                # 提取标题
                title_selector = source_config['selectors']['title']
                title_element = soup.select_one(title_selector)
                title = title_element.get_text().strip() if title_element else link_element.get_text().strip()
                
                # 提取内容
                content_selector = source_config['selectors']['content']
                content_element = soup.select_one(content_selector)
                content = content_element.get_text().strip() if content_element else ""
                
                # 提取时间
                time_selector = source_config['selectors'].get('time')
                publish_time = None
                if time_selector:
                    time_element = soup.select_one(time_selector)
                    if time_element:
                        publish_time = time_element.get_text().strip()
                
                return {
                    "title": title,
                    "url": href,
                    "content": content[:1000],  # 限制内容长度
                    "summary": content[:200] + "..." if len(content) > 200 else content,
                    "publish_time": publish_time,
                    "source_url": source_config['url']
                }
                
        except Exception as e:
            self.log_warning(f"提取文章信息失败: {str(e)}")
            return None
    
    def _contains_keywords(self, text: str, keywords: List[str]) -> bool:
        """检查文本是否包含关键词"""
        text_lower = text.lower()
        return any(keyword.lower() in text_lower for keyword in keywords)
    
    async def _filter_and_score_articles(self, articles: List[Dict], keywords: List[str]) -> List[Dict]:
        """过滤和评分文章"""
        filtered_articles = []
        
        for article in articles:
            # 计算相关性得分
            score = self._calculate_relevance_score(article, keywords)
            
            if score > 0.3:  # 相关性阈值
                article['relevance_score'] = score
                filtered_articles.append(article)
        
        # 按相关性得分排序
        filtered_articles.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return filtered_articles
    
    def _calculate_relevance_score(self, article: Dict, keywords: List[str]) -> float:
        """计算文章相关性得分"""
        title = article.get('title', '').lower()
        content = article.get('content', '').lower()
        
        score = 0.0
        
        for keyword in keywords:
            keyword_lower = keyword.lower()
            
            # 标题中的关键词权重更高
            title_count = title.count(keyword_lower)
            content_count = content.count(keyword_lower)
            
            score += title_count * 0.3 + content_count * 0.1
        
        # 标准化得分
        max_possible_score = len(keywords) * 0.3
        return min(score / max_possible_score, 1.0) if max_possible_score > 0 else 0.0
    
    def _extract_articles_from_text(self, text: str) -> List[Dict]:
        """从文本中提取文章信息"""
        articles = []
        
        # 简单的文本解析逻辑
        lines = text.split('\n')
        current_article = {}
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('标题:') or line.startswith('Title:'):
                if current_article:
                    articles.append(current_article)
                current_article = {'title': line.split(':', 1)[1].strip()}
            elif line.startswith('链接:') or line.startswith('URL:'):
                current_article['url'] = line.split(':', 1)[1].strip()
            elif line.startswith('摘要:') or line.startswith('Summary:'):
                current_article['summary'] = line.split(':', 1)[1].strip()
        
        if current_article:
            articles.append(current_article)
        
        return articles
