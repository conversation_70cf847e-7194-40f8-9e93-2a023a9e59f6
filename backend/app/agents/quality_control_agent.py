"""
质量控制智能体
"""
from typing import Any, Dict, List, Optional, Tuple
from app.agents.base_agent import BaseAgent
import re
import json
from datetime import datetime


class QualityControlAgent(BaseAgent):
    """质量控制智能体 - 负责内容质量检查和评估"""
    
    def __init__(self):
        super().__init__(
            name="QualityControlAgent",
            description="负责对改写后的内容进行全面的质量检查和评估"
        )
        
        # 质量评估标准
        self.quality_standards = {
            "accuracy": {
                "weight": 0.3,
                "criteria": ["事实准确性", "技术描述正确性", "数据引用准确性"]
            },
            "readability": {
                "weight": 0.25,
                "criteria": ["语言流畅性", "逻辑清晰性", "结构合理性"]
            },
            "originality": {
                "weight": 0.2,
                "criteria": ["原创性", "独特观点", "避免抄袭"]
            },
            "engagement": {
                "weight": 0.15,
                "criteria": ["吸引力", "互动性", "实用性"]
            },
            "compliance": {
                "weight": 0.1,
                "criteria": ["内容合规性", "平台规范", "版权问题"]
            }
        }
        
        # 质量阈值
        self.quality_thresholds = {
            "excellent": 8.5,
            "good": 7.0,
            "acceptable": 6.0,
            "needs_improvement": 4.0
        }
    
    def get_required_fields(self) -> List[str]:
        """获取必需的输入字段"""
        return ["content"]
    
    async def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行质量控制任务"""
        content = task.get("content")
        original_content = task.get("original_content")
        check_level = task.get("check_level", "standard")  # basic, standard, strict
        
        self.log_info(f"开始质量检查，检查级别: {check_level}")
        
        try:
            # 1. 基础质量检查
            basic_checks = await self._basic_quality_checks(content)
            
            # 2. 内容准确性检查
            accuracy_check = await self._accuracy_check(content, original_content)
            
            # 3. 可读性评估
            readability_check = await self._readability_assessment(content)
            
            # 4. 原创性检查
            originality_check = await self._originality_check(content, original_content)
            
            # 5. 吸引力评估
            engagement_check = await self._engagement_assessment(content)
            
            # 6. 合规性检查
            compliance_check = await self._compliance_check(content)
            
            # 7. 综合评分
            overall_score = await self._calculate_overall_score({
                "accuracy": accuracy_check["score"],
                "readability": readability_check["score"],
                "originality": originality_check["score"],
                "engagement": engagement_check["score"],
                "compliance": compliance_check["score"]
            })
            
            # 8. 生成改进建议
            improvement_suggestions = await self._generate_improvement_suggestions({
                "basic": basic_checks,
                "accuracy": accuracy_check,
                "readability": readability_check,
                "originality": originality_check,
                "engagement": engagement_check,
                "compliance": compliance_check
            })
            
            # 9. 质量等级判定
            quality_level = self._determine_quality_level(overall_score)
            
            self.log_info(f"质量检查完成，总分: {overall_score:.2f}, 等级: {quality_level}")
            
            return {
                "overall_score": overall_score,
                "quality_level": quality_level,
                "detailed_scores": {
                    "accuracy": accuracy_check["score"],
                    "readability": readability_check["score"],
                    "originality": originality_check["score"],
                    "engagement": engagement_check["score"],
                    "compliance": compliance_check["score"]
                },
                "basic_checks": basic_checks,
                "detailed_analysis": {
                    "accuracy": accuracy_check,
                    "readability": readability_check,
                    "originality": originality_check,
                    "engagement": engagement_check,
                    "compliance": compliance_check
                },
                "improvement_suggestions": improvement_suggestions,
                "is_approved": overall_score >= self.quality_thresholds["acceptable"],
                "check_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.log_error(f"质量检查失败: {str(e)}")
            raise
    
    async def _basic_quality_checks(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """基础质量检查"""
        checks = {
            "has_title": bool(content.get("title", "").strip()),
            "has_content": bool(content.get("content", "").strip()),
            "has_summary": bool(content.get("summary", "").strip()),
            "title_length_ok": 10 <= len(content.get("title", "")) <= 50,
            "content_length_ok": 500 <= len(content.get("content", "")) <= 3000,
            "summary_length_ok": 50 <= len(content.get("summary", "")) <= 200
        }
        
        passed_checks = sum(checks.values())
        total_checks = len(checks)
        
        return {
            "checks": checks,
            "passed": passed_checks,
            "total": total_checks,
            "pass_rate": passed_checks / total_checks
        }
    
    async def _accuracy_check(self, content: Dict[str, Any], original: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """准确性检查"""
        accuracy_prompt = f"""
        请评估以下内容的准确性：
        
        标题: {content.get('title', '')}
        内容: {content.get('content', '')}
        
        评估要点：
        1. 技术描述是否准确
        2. 事实陈述是否正确
        3. 数据引用是否可靠
        4. 专业术语使用是否恰当
        5. 逻辑推理是否合理
        
        请给出1-10分的评分，并说明理由。
        返回JSON格式：{{"score": 分数, "issues": ["问题1", "问题2"], "strengths": ["优点1", "优点2"]}}
        """
        
        try:
            result = self.agent.run(accuracy_prompt)
            
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return self._parse_score_from_text(result, "accuracy")
            
            return result if isinstance(result, dict) else {"score": 7.0, "issues": [], "strengths": []}
            
        except Exception as e:
            self.log_warning(f"准确性检查失败: {str(e)}")
            return {"score": 6.0, "issues": ["无法完成自动检查"], "strengths": []}
    
    async def _readability_assessment(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """可读性评估"""
        readability_prompt = f"""
        请评估以下内容的可读性：
        
        标题: {content.get('title', '')}
        内容: {content.get('content', '')}
        
        评估要点：
        1. 语言表达是否流畅自然
        2. 句子结构是否清晰
        3. 段落划分是否合理
        4. 逻辑结构是否清楚
        5. 专业术语解释是否充分
        6. 整体阅读体验如何
        
        请给出1-10分的评分，并提供具体建议。
        返回JSON格式：{{"score": 分数, "issues": ["问题"], "suggestions": ["建议"]}}
        """
        
        try:
            result = self.agent.run(readability_prompt)
            
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return self._parse_score_from_text(result, "readability")
            
            return result if isinstance(result, dict) else {"score": 7.0, "issues": [], "suggestions": []}
            
        except Exception as e:
            self.log_warning(f"可读性评估失败: {str(e)}")
            return {"score": 6.5, "issues": [], "suggestions": []}
    
    async def _originality_check(self, content: Dict[str, Any], original: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """原创性检查"""
        originality_prompt = f"""
        请评估内容的原创性：
        
        改写后内容: {content.get('content', '')}
        原始内容: {original.get('content', '') if original else '无'}
        
        评估要点：
        1. 是否有独特的观点和见解
        2. 表达方式是否有创新
        3. 是否避免了简单的复制粘贴
        4. 是否增加了价值和深度
        5. 整体原创性水平
        
        请给出1-10分的评分。
        返回JSON格式：{{"score": 分数, "originality_level": "高/中/低", "added_value": ["增值点"]}}
        """
        
        try:
            result = self.agent.run(originality_prompt)
            
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return self._parse_score_from_text(result, "originality")
            
            return result if isinstance(result, dict) else {"score": 7.0, "originality_level": "中", "added_value": []}
            
        except Exception as e:
            self.log_warning(f"原创性检查失败: {str(e)}")
            return {"score": 6.5, "originality_level": "中", "added_value": []}
    
    async def _engagement_assessment(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """吸引力评估"""
        engagement_prompt = f"""
        请评估内容的吸引力和互动性：
        
        标题: {content.get('title', '')}
        内容: {content.get('content', '')}
        
        评估要点：
        1. 标题是否吸引人
        2. 开头是否能抓住读者注意力
        3. 内容是否有趣味性
        4. 是否能引发思考和讨论
        5. 实用性和价值感
        
        请给出1-10分的评分。
        返回JSON格式：{{"score": 分数, "strengths": ["优势"], "improvements": ["改进点"]}}
        """
        
        try:
            result = self.agent.run(engagement_prompt)
            
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return self._parse_score_from_text(result, "engagement")
            
            return result if isinstance(result, dict) else {"score": 7.0, "strengths": [], "improvements": []}
            
        except Exception as e:
            self.log_warning(f"吸引力评估失败: {str(e)}")
            return {"score": 6.5, "strengths": [], "improvements": []}
    
    async def _compliance_check(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """合规性检查"""
        # 简化的合规性检查
        issues = []
        
        content_text = content.get('content', '').lower()
        title_text = content.get('title', '').lower()
        
        # 检查敏感词汇
        sensitive_words = ['违法', '欺诈', '虚假', '误导']
        for word in sensitive_words:
            if word in content_text or word in title_text:
                issues.append(f"包含敏感词汇: {word}")
        
        # 检查内容长度合规性
        if len(content.get('content', '')) > 5000:
            issues.append("内容过长，可能影响阅读体验")
        
        score = 10.0 - len(issues) * 2.0
        score = max(score, 1.0)
        
        return {
            "score": score,
            "issues": issues,
            "is_compliant": len(issues) == 0
        }
    
    async def _calculate_overall_score(self, scores: Dict[str, float]) -> float:
        """计算综合评分"""
        weighted_score = 0.0
        
        for dimension, score in scores.items():
            if dimension in self.quality_standards:
                weight = self.quality_standards[dimension]["weight"]
                weighted_score += score * weight
        
        return round(weighted_score, 2)
    
    async def _generate_improvement_suggestions(self, analysis_results: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 基于各项检查结果生成建议
        if analysis_results["basic"]["pass_rate"] < 0.8:
            suggestions.append("完善基础内容结构，确保标题、正文、摘要完整")
        
        if analysis_results["accuracy"]["score"] < 7.0:
            suggestions.append("提高内容准确性，核实技术描述和事实陈述")
        
        if analysis_results["readability"]["score"] < 7.0:
            suggestions.append("改善可读性，优化语言表达和段落结构")
        
        if analysis_results["originality"]["score"] < 6.0:
            suggestions.append("增强原创性，添加独特观点和深度分析")
        
        if analysis_results["engagement"]["score"] < 6.5:
            suggestions.append("提升内容吸引力，增加互动性和实用价值")
        
        if not analysis_results["compliance"]["is_compliant"]:
            suggestions.append("解决合规性问题，确保内容符合平台规范")
        
        return suggestions
    
    def _determine_quality_level(self, score: float) -> str:
        """判定质量等级"""
        if score >= self.quality_thresholds["excellent"]:
            return "优秀"
        elif score >= self.quality_thresholds["good"]:
            return "良好"
        elif score >= self.quality_thresholds["acceptable"]:
            return "合格"
        else:
            return "需要改进"
    
    def _parse_score_from_text(self, text: str, check_type: str) -> Dict[str, Any]:
        """从文本解析评分结果"""
        # 尝试提取数字评分
        score_match = re.search(r'(\d+(?:\.\d+)?)\s*分', text)
        if score_match:
            score = float(score_match.group(1))
        else:
            score = 7.0  # 默认分数
        
        return {
            "score": min(max(score, 1.0), 10.0),
            "issues": [],
            "suggestions": [],
            "raw_text": text
        }
