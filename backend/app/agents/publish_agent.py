"""
发布智能体
"""
from typing import Any, Dict, List, Optional
from app.agents.base_agent import BaseAgent
import json
from datetime import datetime, timedelta
import asyncio


class PublishAgent(BaseAgent):
    """发布智能体 - 负责内容发布到各个平台"""
    
    def __init__(self):
        super().__init__(
            name="PublishAgent",
            description="负责将审核通过的内容发布到微信公众号等平台"
        )
        
        # 支持的发布平台
        self.supported_platforms = {
            "wechat": {
                "name": "微信公众号",
                "api_endpoint": "/api/wechat/publish",
                "max_title_length": 64,
                "max_content_length": 20000,
                "supported_formats": ["text", "html", "markdown"]
            },
            "weibo": {
                "name": "微博",
                "api_endpoint": "/api/weibo/publish", 
                "max_title_length": 30,
                "max_content_length": 2000,
                "supported_formats": ["text"]
            },
            "toutiao": {
                "name": "今日头条",
                "api_endpoint": "/api/toutiao/publish",
                "max_title_length": 50,
                "max_content_length": 50000,
                "supported_formats": ["text", "html"]
            }
        }
        
        # 发布策略配置
        self.publish_strategies = {
            "immediate": "立即发布",
            "scheduled": "定时发布", 
            "optimal_time": "最佳时间发布",
            "batch": "批量发布"
        }
    
    def get_required_fields(self) -> List[str]:
        """获取必需的输入字段"""
        return ["content", "platform", "publish_strategy"]
    
    async def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行发布任务"""
        content = task.get("content")
        platform = task.get("platform", "wechat")
        publish_strategy = task.get("publish_strategy", "immediate")
        scheduled_time = task.get("scheduled_time")
        publish_config = task.get("publish_config", {})
        
        self.log_info(f"开始发布任务，平台: {platform}, 策略: {publish_strategy}")
        
        try:
            # 1. 验证平台支持
            if platform not in self.supported_platforms:
                raise ValueError(f"不支持的发布平台: {platform}")
            
            # 2. 内容格式化和适配
            formatted_content = await self._format_content_for_platform(content, platform)
            
            # 3. 内容验证
            validation_result = await self._validate_content_for_platform(formatted_content, platform)
            if not validation_result["is_valid"]:
                raise ValueError(f"内容验证失败: {validation_result['errors']}")
            
            # 4. 生成发布配置
            final_config = await self._generate_publish_config(
                formatted_content, platform, publish_strategy, scheduled_time, publish_config
            )
            
            # 5. 执行发布
            publish_result = await self._execute_publish(formatted_content, platform, final_config)
            
            # 6. 发布后处理
            post_publish_result = await self._post_publish_processing(publish_result, platform)
            
            self.log_info(f"发布完成，平台: {platform}, 状态: {publish_result.get('status')}")
            
            return {
                "platform": platform,
                "publish_strategy": publish_strategy,
                "content_id": formatted_content.get("id"),
                "publish_result": publish_result,
                "post_processing": post_publish_result,
                "publish_time": datetime.now().isoformat(),
                "success": publish_result.get("status") == "success"
            }
            
        except Exception as e:
            self.log_error(f"发布失败: {str(e)}")
            raise
    
    async def _format_content_for_platform(self, content: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """为特定平台格式化内容"""
        platform_config = self.supported_platforms[platform]
        
        format_prompt = f"""
        请将以下内容格式化为适合{platform_config['name']}发布的格式：
        
        原始内容：
        标题: {content.get('title', '')}
        正文: {content.get('content', '')}
        摘要: {content.get('summary', '')}
        标签: {content.get('tags', [])}
        
        平台限制：
        - 标题最大长度: {platform_config['max_title_length']}字符
        - 内容最大长度: {platform_config['max_content_length']}字符
        - 支持格式: {', '.join(platform_config['supported_formats'])}
        
        格式化要求：
        1. 确保标题和内容长度符合平台限制
        2. 优化排版和格式
        3. 添加适当的话题标签
        4. 保持内容的完整性和可读性
        
        请返回JSON格式：{{"title": "标题", "content": "内容", "tags": ["标签"], "format": "格式类型"}}
        """
        
        try:
            result = self.agent.run(format_prompt)
            
            if isinstance(result, str):
                try:
                    formatted = json.loads(result)
                except json.JSONDecodeError:
                    formatted = self._parse_formatted_content(result, content, platform)
            else:
                formatted = result if isinstance(result, dict) else {}
            
            # 确保包含原始内容的其他字段
            formatted.update({
                "id": content.get("id"),
                "original_title": content.get("title"),
                "original_content": content.get("content"),
                "platform": platform
            })
            
            return formatted
            
        except Exception as e:
            self.log_warning(f"内容格式化失败: {str(e)}")
            return self._fallback_format_content(content, platform)
    
    async def _validate_content_for_platform(self, content: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """验证内容是否符合平台要求"""
        platform_config = self.supported_platforms[platform]
        errors = []
        warnings = []
        
        # 检查标题长度
        title = content.get("title", "")
        if len(title) > platform_config["max_title_length"]:
            errors.append(f"标题长度超限: {len(title)}/{platform_config['max_title_length']}")
        elif len(title) < 5:
            warnings.append("标题过短，可能影响吸引力")
        
        # 检查内容长度
        content_text = content.get("content", "")
        if len(content_text) > platform_config["max_content_length"]:
            errors.append(f"内容长度超限: {len(content_text)}/{platform_config['max_content_length']}")
        elif len(content_text) < 100:
            warnings.append("内容过短，可能影响质量")
        
        # 检查格式支持
        content_format = content.get("format", "text")
        if content_format not in platform_config["supported_formats"]:
            errors.append(f"不支持的格式: {content_format}")
        
        # 平台特定验证
        if platform == "wechat":
            wechat_validation = await self._validate_wechat_content(content)
            errors.extend(wechat_validation.get("errors", []))
            warnings.extend(wechat_validation.get("warnings", []))
        
        return {
            "is_valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    async def _validate_wechat_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """微信公众号特定验证"""
        errors = []
        warnings = []
        
        content_text = content.get("content", "").lower()
        
        # 检查敏感内容
        sensitive_keywords = ["广告", "推广", "营销", "投资", "理财"]
        for keyword in sensitive_keywords:
            if keyword in content_text:
                warnings.append(f"包含可能的敏感词汇: {keyword}")
        
        # 检查外链
        if "http://" in content_text or "https://" in content_text:
            warnings.append("内容包含外部链接，可能影响推荐")
        
        return {"errors": errors, "warnings": warnings}
    
    async def _generate_publish_config(self, content: Dict[str, Any], platform: str, 
                                     strategy: str, scheduled_time: Optional[str], 
                                     custom_config: Dict[str, Any]) -> Dict[str, Any]:
        """生成发布配置"""
        config = {
            "platform": platform,
            "strategy": strategy,
            "content_id": content.get("id"),
            "retry_count": 3,
            "timeout": 30
        }
        
        # 根据发布策略设置时间
        if strategy == "immediate":
            config["publish_time"] = datetime.now().isoformat()
        elif strategy == "scheduled" and scheduled_time:
            config["publish_time"] = scheduled_time
        elif strategy == "optimal_time":
            optimal_time = await self._calculate_optimal_publish_time(platform)
            config["publish_time"] = optimal_time
        else:
            config["publish_time"] = datetime.now().isoformat()
        
        # 平台特定配置
        if platform == "wechat":
            config.update({
                "send_ignore_reprint": True,
                "need_open_comment": True,
                "only_fans_can_comment": False
            })
        
        # 合并自定义配置
        config.update(custom_config)
        
        return config
    
    async def _calculate_optimal_publish_time(self, platform: str) -> str:
        """计算最佳发布时间"""
        now = datetime.now()
        
        # 基于平台特性的最佳时间
        optimal_hours = {
            "wechat": [8, 12, 18, 21],  # 微信公众号的高活跃时段
            "weibo": [9, 12, 15, 20],   # 微博的高活跃时段
            "toutiao": [7, 11, 14, 19]  # 今日头条的高活跃时段
        }
        
        platform_hours = optimal_hours.get(platform, [9, 12, 18, 21])
        
        # 找到下一个最佳时间点
        for hour in platform_hours:
            optimal_time = now.replace(hour=hour, minute=0, second=0, microsecond=0)
            if optimal_time > now:
                return optimal_time.isoformat()
        
        # 如果今天没有合适时间，选择明天的第一个时间点
        tomorrow = now + timedelta(days=1)
        optimal_time = tomorrow.replace(hour=platform_hours[0], minute=0, second=0, microsecond=0)
        return optimal_time.isoformat()
    
    async def _execute_publish(self, content: Dict[str, Any], platform: str, config: Dict[str, Any]) -> Dict[str, Any]:
        """执行实际发布"""
        # 模拟发布过程（实际实现中需要调用具体平台的API）
        self.log_info(f"模拟发布到{platform}平台")
        
        try:
            # 模拟API调用延迟
            await asyncio.sleep(1)
            
            # 模拟发布结果
            publish_result = {
                "status": "success",
                "platform_post_id": f"{platform}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                "publish_url": f"https://{platform}.example.com/post/{content.get('id')}",
                "publish_time": config.get("publish_time"),
                "platform_response": {
                    "code": 200,
                    "message": "发布成功"
                }
            }
            
            return publish_result
            
        except Exception as e:
            return {
                "status": "failed",
                "error": str(e),
                "retry_count": config.get("retry_count", 0) - 1
            }
    
    async def _post_publish_processing(self, publish_result: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """发布后处理"""
        processing_result = {
            "monitoring_enabled": True,
            "analytics_tracking": True,
            "backup_created": True
        }
        
        if publish_result.get("status") == "success":
            # 启动监控
            processing_result["monitoring_started"] = True
            
            # 记录发布日志
            processing_result["log_recorded"] = True
            
            # 通知相关人员
            processing_result["notification_sent"] = True
        
        return processing_result
    
    def _parse_formatted_content(self, text: str, original: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """从文本解析格式化内容"""
        lines = text.split('\n')
        title = ""
        content = ""
        tags = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('标题:') or line.startswith('title:'):
                title = line.split(':', 1)[1].strip()
            elif line.startswith('内容:') or line.startswith('content:'):
                content = line.split(':', 1)[1].strip()
            elif line.startswith('标签:') or line.startswith('tags:'):
                tag_text = line.split(':', 1)[1].strip()
                tags = [tag.strip() for tag in tag_text.split(',')]
        
        return {
            "title": title or original.get("title", ""),
            "content": content or original.get("content", ""),
            "tags": tags or original.get("tags", []),
            "format": "text"
        }
    
    def _fallback_format_content(self, content: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """备用格式化方法"""
        platform_config = self.supported_platforms[platform]
        
        # 简单截断处理
        title = content.get("title", "")[:platform_config["max_title_length"]]
        content_text = content.get("content", "")[:platform_config["max_content_length"]]
        
        return {
            "title": title,
            "content": content_text,
            "tags": content.get("tags", [])[:5],  # 限制标签数量
            "format": "text",
            "id": content.get("id"),
            "platform": platform
        }
