"""
内容改写智能体
"""
from typing import Any, Dict, List, Optional
from app.agents.base_agent import BaseAgent
import re
import json
from datetime import datetime


class ContentRewriteAgent(BaseAgent):
    """内容改写智能体 - 将原始资讯改写为适合微信公众号的内容"""
    
    def __init__(self):
        super().__init__(
            name="ContentRewriteAgent", 
            description="负责将原始AI资讯改写为适合微信公众号发布的优质内容"
        )
        
        # 微信公众号写作风格配置
        self.wechat_style_config = {
            "tone": "专业而亲和",
            "target_audience": "AI从业者、技术爱好者、企业决策者",
            "content_length": {
                "title": "10-30字",
                "summary": "50-100字", 
                "main_content": "800-2000字"
            },
            "writing_principles": [
                "通俗易懂，避免过度技术化",
                "结构清晰，逻辑性强",
                "观点鲜明，有独特见解",
                "贴近实际应用场景",
                "引发读者思考和讨论"
            ]
        }
    
    def get_required_fields(self) -> List[str]:
        """获取必需的输入字段"""
        return ["original_content"]
    
    async def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行内容改写任务"""
        original_content = task.get("original_content")
        rewrite_style = task.get("style", "wechat_official")
        target_length = task.get("target_length", "medium")
        
        self.log_info(f"开始改写内容，风格: {rewrite_style}")
        
        try:
            # 分析原始内容
            content_analysis = await self._analyze_original_content(original_content)
            
            # 生成改写后的内容
            rewritten_content = await self._rewrite_content(
                original_content, 
                content_analysis,
                rewrite_style,
                target_length
            )
            
            # 质量检查和优化
            optimized_content = await self._optimize_content(rewritten_content)
            
            # 生成SEO友好的标签和关键词
            seo_data = await self._generate_seo_data(optimized_content)
            
            self.log_info("内容改写完成")
            
            return {
                "original_title": original_content.get("title", ""),
                "rewritten_title": optimized_content["title"],
                "original_content": original_content.get("content", ""),
                "rewritten_content": optimized_content["content"],
                "summary": optimized_content["summary"],
                "tags": seo_data["tags"],
                "keywords": seo_data["keywords"],
                "readability_score": optimized_content["readability_score"],
                "word_count": len(optimized_content["content"]),
                "rewrite_time": datetime.now().isoformat(),
                "style": rewrite_style
            }
            
        except Exception as e:
            self.log_error(f"内容改写失败: {str(e)}")
            raise
    
    async def _analyze_original_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """分析原始内容"""
        analysis_prompt = f"""
        请分析以下AI资讯内容，提取关键信息：
        
        标题: {content.get('title', '')}
        内容: {content.get('content', '')}
        来源: {content.get('source_url', '')}
        
        请分析并返回以下信息：
        1. 主要话题和技术领域
        2. 核心观点和亮点
        3. 目标受众群体
        4. 内容的新闻价值和影响力
        5. 可以展开的角度和观点
        6. 相关的技术背景知识
        
        请以JSON格式返回分析结果。
        """
        
        try:
            result = self.agent.run(analysis_prompt)
            
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return self._parse_analysis_from_text(result)
            
            return result if isinstance(result, dict) else {}
            
        except Exception as e:
            self.log_warning(f"内容分析失败: {str(e)}")
            return self._fallback_analysis(content)
    
    async def _rewrite_content(self, original: Dict, analysis: Dict, style: str, length: str) -> Dict[str, Any]:
        """改写内容"""
        length_config = {
            "short": "500-800字",
            "medium": "800-1500字", 
            "long": "1500-2500字"
        }
        
        target_length_desc = length_config.get(length, "800-1500字")
        
        rewrite_prompt = f"""
        作为一名专业的AI科技内容编辑，请将以下原始资讯改写为适合微信公众号发布的优质内容。
        
        原始内容：
        标题: {original.get('title', '')}
        内容: {original.get('content', '')}
        
        内容分析：
        {json.dumps(analysis, ensure_ascii=False, indent=2)}
        
        改写要求：
        1. 风格：{self.wechat_style_config['tone']}
        2. 目标受众：{self.wechat_style_config['target_audience']}
        3. 内容长度：{target_length_desc}
        4. 写作原则：{', '.join(self.wechat_style_config['writing_principles'])}
        
        请提供：
        1. 吸引人的标题（10-30字）
        2. 精彩的开头段落
        3. 结构清晰的正文内容
        4. 简洁有力的总结
        5. 50-100字的内容摘要
        
        注意事项：
        - 保持内容的准确性和专业性
        - 增加可读性和趣味性
        - 适当添加个人观点和见解
        - 使用通俗易懂的语言解释技术概念
        - 结合实际应用场景和案例
        
        请以JSON格式返回改写结果，包含title、content、summary字段。
        """
        
        try:
            result = self.agent.run(rewrite_prompt)
            
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return self._parse_rewrite_from_text(result)
            
            return result if isinstance(result, dict) else {}
            
        except Exception as e:
            self.log_warning(f"内容改写失败: {str(e)}")
            return self._fallback_rewrite(original)
    
    async def _optimize_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """优化内容质量"""
        optimization_prompt = f"""
        请对以下改写后的内容进行质量优化：
        
        标题: {content.get('title', '')}
        内容: {content.get('content', '')}
        摘要: {content.get('summary', '')}
        
        优化要求：
        1. 检查语法和表达是否流畅
        2. 确保逻辑结构清晰
        3. 优化段落划分和排版
        4. 增强可读性和吸引力
        5. 确保内容的准确性
        
        请返回优化后的内容，并给出可读性评分（1-10分）。
        格式：JSON，包含title、content、summary、readability_score字段。
        """
        
        try:
            result = self.agent.run(optimization_prompt)
            
            if isinstance(result, str):
                try:
                    optimized = json.loads(result)
                except json.JSONDecodeError:
                    optimized = self._parse_optimization_from_text(result)
            else:
                optimized = result if isinstance(result, dict) else {}
            
            # 确保包含所有必要字段
            optimized.setdefault('title', content.get('title', ''))
            optimized.setdefault('content', content.get('content', ''))
            optimized.setdefault('summary', content.get('summary', ''))
            optimized.setdefault('readability_score', 7.0)
            
            return optimized
            
        except Exception as e:
            self.log_warning(f"内容优化失败: {str(e)}")
            content['readability_score'] = 6.0
            return content
    
    async def _generate_seo_data(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """生成SEO数据"""
        seo_prompt = f"""
        基于以下内容，生成SEO友好的标签和关键词：
        
        标题: {content.get('title', '')}
        内容: {content.get('content', '')}
        
        请生成：
        1. 5-10个相关标签（用于分类和检索）
        2. 10-15个关键词（用于搜索优化）
        3. 确保标签和关键词与AI、技术、创新等主题相关
        
        返回JSON格式：{"tags": [...], "keywords": [...]}
        """
        
        try:
            result = self.agent.run(seo_prompt)
            
            if isinstance(result, str):
                try:
                    return json.loads(result)
                except json.JSONDecodeError:
                    return self._parse_seo_from_text(result)
            
            return result if isinstance(result, dict) else {}
            
        except Exception as e:
            self.log_warning(f"SEO数据生成失败: {str(e)}")
            return self._fallback_seo_data(content)
    
    def _parse_analysis_from_text(self, text: str) -> Dict[str, Any]:
        """从文本解析分析结果"""
        return {
            "main_topic": "AI技术",
            "key_points": ["技术创新", "应用场景"],
            "target_audience": "技术从业者",
            "news_value": "中等",
            "expansion_angles": ["技术原理", "商业应用"],
            "background_knowledge": ["机器学习", "深度学习"]
        }
    
    def _parse_rewrite_from_text(self, text: str) -> Dict[str, Any]:
        """从文本解析改写结果"""
        lines = text.split('\n')
        title = ""
        content = ""
        summary = ""
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            if '标题' in line or 'title' in line.lower():
                current_section = 'title'
                title = line.split(':', 1)[-1].strip()
            elif '内容' in line or 'content' in line.lower():
                current_section = 'content'
            elif '摘要' in line or 'summary' in line.lower():
                current_section = 'summary'
            elif line and current_section:
                if current_section == 'content':
                    content += line + '\n'
                elif current_section == 'summary':
                    summary += line + ' '
        
        return {
            "title": title or "AI技术新进展",
            "content": content.strip() or "内容正在生成中...",
            "summary": summary.strip() or "AI技术相关资讯摘要"
        }
    
    def _parse_optimization_from_text(self, text: str) -> Dict[str, Any]:
        """从文本解析优化结果"""
        result = self._parse_rewrite_from_text(text)
        result['readability_score'] = 7.0
        return result
    
    def _parse_seo_from_text(self, text: str) -> Dict[str, Any]:
        """从文本解析SEO数据"""
        tags = ["AI", "人工智能", "技术创新", "机器学习", "深度学习"]
        keywords = ["人工智能", "AI技术", "机器学习", "深度学习", "技术创新", 
                   "科技发展", "智能化", "自动化", "算法", "数据科学"]
        
        return {"tags": tags, "keywords": keywords}
    
    def _fallback_analysis(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """备用分析方法"""
        return {
            "main_topic": "AI技术资讯",
            "key_points": ["技术发展", "行业动态"],
            "target_audience": "AI从业者",
            "news_value": "中等",
            "expansion_angles": ["技术分析", "市场影响"],
            "background_knowledge": ["人工智能基础"]
        }
    
    def _fallback_rewrite(self, original: Dict[str, Any]) -> Dict[str, Any]:
        """备用改写方法"""
        original_title = original.get('title', '')
        original_content = original.get('content', '')
        
        # 简单的改写逻辑
        new_title = f"深度解析：{original_title}"
        new_content = f"""
        {original_content}
        
        【编者观点】
        这一技术发展对AI行业具有重要意义，值得我们深入关注和思考。
        
        【应用前景】
        该技术在实际应用中具有广阔的前景，将为相关行业带来新的机遇。
        """
        
        summary = original_content[:100] + "..." if len(original_content) > 100 else original_content
        
        return {
            "title": new_title,
            "content": new_content,
            "summary": summary
        }
    
    def _fallback_seo_data(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """备用SEO数据"""
        return {
            "tags": ["AI", "人工智能", "技术", "创新", "科技"],
            "keywords": ["人工智能", "AI技术", "机器学习", "深度学习", "技术创新"]
        }
