"""
基础智能体类
"""
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from smolagents import CodeAgent, InferenceClientModel
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """基础智能体抽象类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.model = InferenceClientModel(
            model_id=settings.openai_model,
            token=settings.openai_api_key
        )
        self.agent = CodeAgent(
            tools=[],
            model=self.model,
            max_iterations=5
        )
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    async def execute(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行任务的抽象方法"""
        pass
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(f"[{self.name}] {message}")
    
    def log_error(self, message: str, error: Exception = None):
        """记录错误日志"""
        if error:
            self.logger.error(f"[{self.name}] {message}: {str(error)}")
        else:
            self.logger.error(f"[{self.name}] {message}")
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(f"[{self.name}] {message}")
    
    async def validate_input(self, task: Dict[str, Any]) -> bool:
        """验证输入参数"""
        required_fields = self.get_required_fields()
        for field in required_fields:
            if field not in task:
                self.log_error(f"缺少必需参数: {field}")
                return False
        return True
    
    @abstractmethod
    def get_required_fields(self) -> List[str]:
        """获取必需的输入字段"""
        pass
    
    def format_result(self, success: bool, data: Any = None, error: str = None) -> Dict[str, Any]:
        """格式化返回结果"""
        result = {
            "agent": self.name,
            "success": success,
            "timestamp": self._get_timestamp()
        }
        
        if success:
            result["data"] = data
        else:
            result["error"] = error
        
        return result
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    async def run_with_error_handling(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """带错误处理的执行方法"""
        try:
            # 验证输入
            if not await self.validate_input(task):
                return self.format_result(False, error="输入参数验证失败")
            
            # 执行任务
            self.log_info(f"开始执行任务: {task.get('task_id', 'unknown')}")
            result = await self.execute(task)
            self.log_info(f"任务执行完成: {task.get('task_id', 'unknown')}")
            
            return self.format_result(True, data=result)
            
        except Exception as e:
            self.log_error("任务执行失败", e)
            return self.format_result(False, error=str(e))
