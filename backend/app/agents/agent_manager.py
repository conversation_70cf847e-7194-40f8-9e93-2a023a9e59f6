"""
智能体管理器
"""
from typing import Any, Dict, List, Optional, Type
from app.agents.base_agent import BaseAgent
from app.agents.news_scraping_agent import NewsScrapingAgent
from app.agents.content_rewrite_agent import ContentRewriteAgent
from app.agents.quality_control_agent import QualityControlAgent
from app.agents.publish_agent import PublishAgent
import asyncio
import logging

logger = logging.getLogger(__name__)


class AgentManager:
    """智能体管理器 - 统一管理和协调所有智能体"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.workflows: Dict[str, List[str]] = {}
        self._initialize_agents()
        self._setup_workflows()
    
    def _initialize_agents(self):
        """初始化所有智能体"""
        self.agents = {
            "news_scraping": NewsScrapingAgent(),
            "content_rewrite": ContentRewriteAgent(),
            "quality_control": QualityControlAgent(),
            "publish": PublishAgent()
        }
        
        logger.info(f"已初始化 {len(self.agents)} 个智能体")
    
    def _setup_workflows(self):
        """设置工作流程"""
        self.workflows = {
            "full_pipeline": [
                "news_scraping",
                "content_rewrite", 
                "quality_control",
                "publish"
            ],
            "content_only": [
                "content_rewrite",
                "quality_control"
            ],
            "scraping_only": [
                "news_scraping"
            ],
            "publish_only": [
                "quality_control",
                "publish"
            ]
        }
        
        logger.info(f"已设置 {len(self.workflows)} 个工作流程")
    
    async def execute_workflow(self, workflow_name: str, initial_task: Dict[str, Any]) -> Dict[str, Any]:
        """执行完整工作流程"""
        if workflow_name not in self.workflows:
            raise ValueError(f"未知的工作流程: {workflow_name}")
        
        agent_sequence = self.workflows[workflow_name]
        logger.info(f"开始执行工作流程: {workflow_name}, 包含 {len(agent_sequence)} 个步骤")
        
        current_data = initial_task.copy()
        workflow_results = {
            "workflow_name": workflow_name,
            "steps": [],
            "success": True,
            "error": None
        }
        
        try:
            for i, agent_name in enumerate(agent_sequence):
                step_start_time = asyncio.get_event_loop().time()
                
                logger.info(f"执行步骤 {i+1}/{len(agent_sequence)}: {agent_name}")
                
                # 执行当前智能体
                agent = self.agents[agent_name]
                step_result = await agent.run_with_error_handling(current_data)
                
                step_end_time = asyncio.get_event_loop().time()
                step_duration = step_end_time - step_start_time
                
                # 记录步骤结果
                step_info = {
                    "step_number": i + 1,
                    "agent_name": agent_name,
                    "success": step_result.get("success", False),
                    "duration": round(step_duration, 2),
                    "result": step_result
                }
                workflow_results["steps"].append(step_info)
                
                # 检查步骤是否成功
                if not step_result.get("success", False):
                    workflow_results["success"] = False
                    workflow_results["error"] = f"步骤 {i+1} ({agent_name}) 执行失败: {step_result.get('error')}"
                    logger.error(workflow_results["error"])
                    break
                
                # 准备下一步的输入数据
                current_data = self._prepare_next_step_data(current_data, step_result, agent_name)
                
                logger.info(f"步骤 {i+1} 完成，耗时 {step_duration:.2f}s")
            
            if workflow_results["success"]:
                logger.info(f"工作流程 {workflow_name} 执行成功")
            
            return workflow_results
            
        except Exception as e:
            workflow_results["success"] = False
            workflow_results["error"] = f"工作流程执行异常: {str(e)}"
            logger.error(workflow_results["error"])
            return workflow_results
    
    def _prepare_next_step_data(self, current_data: Dict[str, Any], 
                               step_result: Dict[str, Any], 
                               current_agent: str) -> Dict[str, Any]:
        """准备下一步的输入数据"""
        next_data = current_data.copy()
        
        # 根据当前智能体的输出，准备下一步的输入
        if current_agent == "news_scraping":
            # 爬取完成后，为改写智能体准备数据
            articles = step_result.get("data", {}).get("articles", [])
            if articles:
                # 选择第一篇文章进行改写（实际应用中可能需要批量处理）
                next_data["original_content"] = articles[0]
        
        elif current_agent == "content_rewrite":
            # 改写完成后，为质量控制智能体准备数据
            rewritten_data = step_result.get("data", {})
            next_data["content"] = {
                "title": rewritten_data.get("rewritten_title"),
                "content": rewritten_data.get("rewritten_content"),
                "summary": rewritten_data.get("summary"),
                "tags": rewritten_data.get("tags", []),
                "keywords": rewritten_data.get("keywords", [])
            }
            next_data["original_content"] = {
                "title": rewritten_data.get("original_title"),
                "content": rewritten_data.get("original_content")
            }
        
        elif current_agent == "quality_control":
            # 质量控制完成后，为发布智能体准备数据
            quality_result = step_result.get("data", {})
            if quality_result.get("is_approved", False):
                # 只有通过质量检查的内容才能发布
                next_data["content"] = current_data.get("content", {})
                next_data["quality_score"] = quality_result.get("overall_score")
            else:
                # 质量不合格，停止流程
                raise ValueError(f"内容质量不合格，评分: {quality_result.get('overall_score', 0)}")
        
        return next_data
    
    async def execute_single_agent(self, agent_name: str, task: Dict[str, Any]) -> Dict[str, Any]:
        """执行单个智能体"""
        if agent_name not in self.agents:
            raise ValueError(f"未知的智能体: {agent_name}")
        
        agent = self.agents[agent_name]
        logger.info(f"执行单个智能体: {agent_name}")
        
        return await agent.run_with_error_handling(task)
    
    async def batch_execute(self, agent_name: str, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量执行智能体任务"""
        if agent_name not in self.agents:
            raise ValueError(f"未知的智能体: {agent_name}")
        
        agent = self.agents[agent_name]
        logger.info(f"批量执行智能体 {agent_name}，任务数量: {len(tasks)}")
        
        # 并发执行任务
        semaphore = asyncio.Semaphore(5)  # 限制并发数
        
        async def execute_with_semaphore(task):
            async with semaphore:
                return await agent.run_with_error_handling(task)
        
        results = await asyncio.gather(
            *[execute_with_semaphore(task) for task in tasks],
            return_exceptions=True
        )
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "success": False,
                    "error": str(result),
                    "task_index": i
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def get_agent_info(self, agent_name: Optional[str] = None) -> Dict[str, Any]:
        """获取智能体信息"""
        if agent_name:
            if agent_name not in self.agents:
                raise ValueError(f"未知的智能体: {agent_name}")
            
            agent = self.agents[agent_name]
            return {
                "name": agent.name,
                "description": agent.description,
                "required_fields": agent.get_required_fields()
            }
        else:
            return {
                agent_name: {
                    "name": agent.name,
                    "description": agent.description,
                    "required_fields": agent.get_required_fields()
                }
                for agent_name, agent in self.agents.items()
            }
    
    def get_workflow_info(self, workflow_name: Optional[str] = None) -> Dict[str, Any]:
        """获取工作流程信息"""
        if workflow_name:
            if workflow_name not in self.workflows:
                raise ValueError(f"未知的工作流程: {workflow_name}")
            
            return {
                "name": workflow_name,
                "steps": self.workflows[workflow_name],
                "description": self._get_workflow_description(workflow_name)
            }
        else:
            return {
                workflow_name: {
                    "steps": steps,
                    "description": self._get_workflow_description(workflow_name)
                }
                for workflow_name, steps in self.workflows.items()
            }
    
    def _get_workflow_description(self, workflow_name: str) -> str:
        """获取工作流程描述"""
        descriptions = {
            "full_pipeline": "完整的AI资讯处理流程：爬取 -> 改写 -> 质检 -> 发布",
            "content_only": "仅内容处理流程：改写 -> 质检",
            "scraping_only": "仅爬取流程：从资讯源获取内容",
            "publish_only": "仅发布流程：质检 -> 发布"
        }
        return descriptions.get(workflow_name, "自定义工作流程")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "manager_status": "healthy",
            "agents_count": len(self.agents),
            "workflows_count": len(self.workflows),
            "agents_health": {}
        }
        
        # 检查每个智能体的健康状态
        for agent_name, agent in self.agents.items():
            try:
                # 简单的健康检查任务
                test_task = {"test": True}
                await asyncio.wait_for(
                    agent.validate_input(test_task), 
                    timeout=5.0
                )
                health_status["agents_health"][agent_name] = "healthy"
            except Exception as e:
                health_status["agents_health"][agent_name] = f"unhealthy: {str(e)}"
        
        return health_status


# 全局智能体管理器实例
agent_manager = AgentManager()
