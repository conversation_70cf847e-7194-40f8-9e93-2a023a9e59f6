"""
Celery应用配置
"""
from celery import Celery
from app.core.config import settings

# 创建Celery应用实例
celery_app = Celery(
    "ai_news_agent",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=[
        "app.tasks.news_tasks",
        "app.tasks.content_tasks", 
        "app.tasks.publish_tasks"
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    
    # 时区设置
    timezone="Asia/Shanghai",
    enable_utc=True,
    
    # 任务路由
    task_routes={
        "app.tasks.news_tasks.*": {"queue": "news"},
        "app.tasks.content_tasks.*": {"queue": "content"},
        "app.tasks.publish_tasks.*": {"queue": "publish"},
    },
    
    # 任务结果过期时间
    result_expires=3600,
    
    # 任务重试配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # 定时任务配置
    beat_schedule={
        "crawl-news-hourly": {
            "task": "app.tasks.news_tasks.crawl_all_sources",
            "schedule": 3600.0,  # 每小时执行一次
        },
        "cleanup-old-tasks": {
            "task": "app.tasks.maintenance_tasks.cleanup_old_tasks",
            "schedule": 86400.0,  # 每天执行一次
        },
    },
)

# 任务装饰器
def task(*args, **kwargs):
    """任务装饰器"""
    return celery_app.task(*args, **kwargs)
