"""
内容处理相关的Celery任务
"""
from celery import current_task
from app.core.celery_app import task
from app.agents.agent_manager import agent_manager
from app.db.base import SessionLocal
from app.models import Content, Task, TaskStatus
from app.tasks.news_tasks import update_task_status
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


@task(bind=True, name="rewrite_content")
def rewrite_content(self, content_id: int, style: str = "wechat_official", 
                   target_length: str = "medium") -> Dict[str, Any]:
    """改写单个内容"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=10, current_step="获取原始内容")
        
        db = SessionLocal()
        
        # 获取原始内容
        content = db.query(Content).filter(Content.id == content_id).first()
        if not content:
            raise ValueError(f"内容不存在: {content_id}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=30, current_step="准备改写任务")
        
        # 准备改写任务
        rewrite_task = {
            "original_content": {
                "title": content.original_title or content.title,
                "content": content.original_content or content.content,
                "url": content.source_url,
                "tags": content.tags or []
            },
            "style": style,
            "target_length": target_length,
            "content_id": content_id
        }
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=50, current_step="执行内容改写")
        
        # 执行改写
        import asyncio
        result = asyncio.run(agent_manager.execute_single_agent("content_rewrite", rewrite_task))
        
        if not result.get("success"):
            raise Exception(f"改写失败: {result.get('error')}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=80, current_step="保存改写结果")
        
        # 保存改写结果
        rewrite_data = result.get("data", {})
        
        content.title = rewrite_data.get("rewritten_title", content.title)
        content.content = rewrite_data.get("rewritten_content", content.content)
        content.summary = rewrite_data.get("summary", content.summary)
        content.tags = rewrite_data.get("tags", content.tags or [])
        content.keywords = rewrite_data.get("keywords", [])
        content.readability_score = int(rewrite_data.get("readability_score", 0) * 10)
        content.word_count = rewrite_data.get("word_count", 0)
        
        db.commit()
        db.close()
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "content_id": content_id,
            "rewrite_data": rewrite_data
        }
        
    except Exception as e:
        logger.error(f"内容改写任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="quality_check_content")
def quality_check_content(self, content_id: int, check_level: str = "standard") -> Dict[str, Any]:
    """质量检查单个内容"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=10, current_step="获取内容")
        
        db = SessionLocal()
        
        # 获取内容
        content = db.query(Content).filter(Content.id == content_id).first()
        if not content:
            raise ValueError(f"内容不存在: {content_id}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=30, current_step="准备质检任务")
        
        # 准备质检任务
        quality_task = {
            "content": {
                "title": content.title,
                "content": content.content,
                "summary": content.summary,
                "tags": content.tags or []
            },
            "original_content": {
                "title": content.original_title or content.title,
                "content": content.original_content or content.content
            },
            "check_level": check_level,
            "content_id": content_id
        }
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=50, current_step="执行质量检查")
        
        # 执行质检
        import asyncio
        result = asyncio.run(agent_manager.execute_single_agent("quality_control", quality_task))
        
        if not result.get("success"):
            raise Exception(f"质检失败: {result.get('error')}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=80, current_step="保存质检结果")
        
        # 保存质检结果
        quality_data = result.get("data", {})
        
        content.quality_score = int(quality_data.get("overall_score", 0) * 10)
        content.readability_score = int(quality_data.get("detailed_scores", {}).get("readability", 0) * 10)
        content.originality_score = int(quality_data.get("detailed_scores", {}).get("originality", 0) * 10)
        content.is_approved = quality_data.get("is_approved", False)
        
        # 保存质检详情到metadata
        if not content.metadata:
            content.metadata = {}
        content.metadata["quality_check"] = quality_data
        
        db.commit()
        db.close()
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "content_id": content_id,
            "quality_data": quality_data
        }
        
    except Exception as e:
        logger.error(f"质量检查任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="batch_process_content")
def batch_process_content(self, content_ids: List[int], 
                         include_rewrite: bool = True,
                         include_quality_check: bool = True,
                         rewrite_style: str = "wechat_official") -> Dict[str, Any]:
    """批量处理内容"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=5, current_step="初始化批量处理")
        
        total_contents = len(content_ids)
        processed_results = []
        
        for i, content_id in enumerate(content_ids):
            try:
                progress = 10 + (i * 80 // total_contents)
                update_task_status(
                    task_id,
                    TaskStatus.RUNNING,
                    progress=progress,
                    current_step=f"处理内容 {i+1}/{total_contents}"
                )
                
                content_result = {"content_id": content_id, "success": True}
                
                # 执行改写
                if include_rewrite:
                    rewrite_result = rewrite_content.apply_async(
                        args=[content_id, rewrite_style]
                    ).get(timeout=180)  # 3分钟超时
                    
                    content_result["rewrite"] = rewrite_result
                    if not rewrite_result.get("success"):
                        content_result["success"] = False
                
                # 执行质检
                if include_quality_check:
                    quality_result = quality_check_content.apply_async(
                        args=[content_id]
                    ).get(timeout=120)  # 2分钟超时
                    
                    content_result["quality_check"] = quality_result
                    if not quality_result.get("success"):
                        content_result["success"] = False
                
                processed_results.append(content_result)
                
            except Exception as e:
                logger.error(f"处理内容 {content_id} 失败: {str(e)}")
                processed_results.append({
                    "content_id": content_id,
                    "success": False,
                    "error": str(e)
                })
        
        # 统计结果
        successful_count = sum(1 for r in processed_results if r.get("success"))
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "total_contents": total_contents,
            "successful_count": successful_count,
            "success_rate": successful_count / total_contents if total_contents > 0 else 0,
            "results": processed_results
        }
        
    except Exception as e:
        logger.error(f"批量处理任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="auto_process_new_content")
def auto_process_new_content(self, hours_back: int = 24) -> Dict[str, Any]:
    """自动处理新内容"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=10, current_step="查找新内容")
        
        db = SessionLocal()
        
        # 查找最近的未处理内容
        from datetime import datetime, timedelta
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        new_contents = db.query(Content).filter(
            Content.created_at >= cutoff_time,
            Content.is_processed == False
        ).all()
        
        if not new_contents:
            db.close()
            update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
            return {"success": True, "message": "没有新的待处理内容"}
        
        content_ids = [content.id for content in new_contents]
        db.close()
        
        update_task_status(
            task_id,
            TaskStatus.RUNNING,
            progress=20,
            current_step=f"找到 {len(content_ids)} 个新内容，开始批量处理"
        )
        
        # 调用批量处理任务
        batch_result = batch_process_content.apply_async(
            args=[content_ids, True, True]
        ).get(timeout=3600)  # 1小时超时
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
        
        return {
            "success": True,
            "new_contents_found": len(content_ids),
            "batch_result": batch_result
        }
        
    except Exception as e:
        logger.error(f"自动处理新内容任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="generate_content_report")
def generate_content_report(self, date_range_days: int = 7) -> Dict[str, Any]:
    """生成内容处理报告"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=20, current_step="收集数据")
        
        db = SessionLocal()
        
        from datetime import datetime, timedelta
        from sqlalchemy import func
        
        cutoff_date = datetime.now() - timedelta(days=date_range_days)
        
        # 统计数据
        total_contents = db.query(Content).filter(Content.created_at >= cutoff_date).count()
        processed_contents = db.query(Content).filter(
            Content.created_at >= cutoff_date,
            Content.is_processed == True
        ).count()
        approved_contents = db.query(Content).filter(
            Content.created_at >= cutoff_date,
            Content.is_approved == True
        ).count()
        
        # 平均质量分数
        avg_quality = db.query(func.avg(Content.quality_score)).filter(
            Content.created_at >= cutoff_date,
            Content.quality_score.isnot(None)
        ).scalar() or 0
        
        # 按来源统计
        source_stats = db.query(
            Content.source_name,
            func.count(Content.id).label('count'),
            func.avg(Content.quality_score).label('avg_quality')
        ).filter(
            Content.created_at >= cutoff_date
        ).group_by(Content.source_name).all()
        
        # 按类别统计
        category_stats = db.query(
            Content.category,
            func.count(Content.id).label('count'),
            func.avg(Content.quality_score).label('avg_quality')
        ).filter(
            Content.created_at >= cutoff_date
        ).group_by(Content.category).all()
        
        db.close()
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=80, current_step="生成报告")
        
        report = {
            "period": f"最近 {date_range_days} 天",
            "summary": {
                "total_contents": total_contents,
                "processed_contents": processed_contents,
                "approved_contents": approved_contents,
                "processing_rate": processed_contents / total_contents if total_contents > 0 else 0,
                "approval_rate": approved_contents / processed_contents if processed_contents > 0 else 0,
                "average_quality_score": round(avg_quality, 2)
            },
            "source_statistics": [
                {
                    "source_name": stat.source_name,
                    "content_count": stat.count,
                    "average_quality": round(stat.avg_quality or 0, 2)
                }
                for stat in source_stats
            ],
            "category_statistics": [
                {
                    "category": stat.category,
                    "content_count": stat.count,
                    "average_quality": round(stat.avg_quality or 0, 2)
                }
                for stat in category_stats
            ]
        }
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "report": report
        }
        
    except Exception as e:
        logger.error(f"生成内容报告任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }
