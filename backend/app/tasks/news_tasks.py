"""
资讯相关的Celery任务
"""
from celery import current_task
from app.core.celery_app import task
from app.agents.agent_manager import agent_manager
from app.db.base import SessionLocal
from app.models import NewsSource, Content, Task, TaskStatus
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


@task(bind=True, name="crawl_news_source")
def crawl_news_source(self, source_id: int, max_articles: int = 10) -> Dict[str, Any]:
    """爬取单个资讯源"""
    task_id = self.request.id
    
    try:
        # 更新任务状态
        update_task_status(task_id, TaskStatus.RUNNING, progress=10)
        
        db = SessionLocal()
        
        # 获取资讯源配置
        news_source = db.query(NewsSource).filter(NewsSource.id == source_id).first()
        if not news_source:
            raise ValueError(f"资讯源不存在: {source_id}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=20, current_step="准备爬取配置")
        
        # 准备爬取任务
        scraping_task = {
            "source_config": {
                "url": news_source.url,
                "selectors": news_source.selectors or {},
                "headers": news_source.headers or {}
            },
            "max_articles": max_articles,
            "keywords": news_source.keywords or ["AI", "人工智能", "机器学习"],
            "task_id": task_id
        }
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=30, current_step="执行爬取")
        
        # 执行爬取
        import asyncio
        result = asyncio.run(agent_manager.execute_single_agent("news_scraping", scraping_task))
        
        if not result.get("success"):
            raise Exception(f"爬取失败: {result.get('error')}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=70, current_step="保存数据")
        
        # 保存爬取结果到数据库
        articles_data = result.get("data", {}).get("articles", [])
        saved_count = 0
        
        for article_data in articles_data:
            try:
                content = Content(
                    title=article_data.get("title"),
                    original_title=article_data.get("title"),
                    content=article_data.get("content"),
                    original_content=article_data.get("content"),
                    summary=article_data.get("summary"),
                    source_url=article_data.get("url"),
                    source_name=news_source.name,
                    news_source_id=source_id,
                    category=news_source.category,
                    tags=article_data.get("tags", []),
                    quality_score=int(article_data.get("relevance_score", 0.5) * 100),
                    is_processed=False
                )
                
                db.add(content)
                saved_count += 1
                
            except Exception as e:
                logger.warning(f"保存文章失败: {str(e)}")
                continue
        
        db.commit()
        
        # 更新资讯源统计
        news_source.total_crawled += saved_count
        news_source.last_crawl_time = result.get("data", {}).get("crawl_time")
        if saved_count > 0:
            news_source.last_success_time = result.get("data", {}).get("crawl_time")
        
        db.commit()
        db.close()
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "source_id": source_id,
            "articles_found": len(articles_data),
            "articles_saved": saved_count,
            "crawl_time": result.get("data", {}).get("crawl_time")
        }
        
    except Exception as e:
        logger.error(f"爬取任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="crawl_all_sources")
def crawl_all_sources(self, max_articles_per_source: int = 10) -> Dict[str, Any]:
    """爬取所有活跃的资讯源"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=5)
        
        db = SessionLocal()
        
        # 获取所有活跃的资讯源
        active_sources = db.query(NewsSource).filter(NewsSource.is_active == True).all()
        
        if not active_sources:
            db.close()
            update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
            return {"success": True, "message": "没有活跃的资讯源"}
        
        total_sources = len(active_sources)
        results = []
        
        for i, source in enumerate(active_sources):
            try:
                progress = 10 + (i * 80 // total_sources)
                update_task_status(
                    task_id, 
                    TaskStatus.RUNNING, 
                    progress=progress,
                    current_step=f"爬取资讯源: {source.name}"
                )
                
                # 调用单个资讯源爬取任务
                result = crawl_news_source.apply_async(
                    args=[source.id, max_articles_per_source]
                ).get(timeout=300)  # 5分钟超时
                
                results.append({
                    "source_id": source.id,
                    "source_name": source.name,
                    "result": result
                })
                
            except Exception as e:
                logger.error(f"爬取资讯源 {source.name} 失败: {str(e)}")
                results.append({
                    "source_id": source.id,
                    "source_name": source.name,
                    "result": {"success": False, "error": str(e)}
                })
        
        db.close()
        
        # 统计结果
        successful_sources = sum(1 for r in results if r["result"].get("success"))
        total_articles = sum(r["result"].get("articles_saved", 0) for r in results)
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
        
        return {
            "success": True,
            "total_sources": total_sources,
            "successful_sources": successful_sources,
            "total_articles_saved": total_articles,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"批量爬取任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="process_crawled_content")
def process_crawled_content(self, content_ids: List[int]) -> Dict[str, Any]:
    """处理爬取到的内容（改写+质检）"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=10)
        
        db = SessionLocal()
        
        # 获取待处理的内容
        contents = db.query(Content).filter(
            Content.id.in_(content_ids),
            Content.is_processed == False
        ).all()
        
        if not contents:
            db.close()
            update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
            return {"success": True, "message": "没有待处理的内容"}
        
        total_contents = len(contents)
        processed_count = 0
        
        for i, content in enumerate(contents):
            try:
                progress = 20 + (i * 60 // total_contents)
                update_task_status(
                    task_id,
                    TaskStatus.RUNNING,
                    progress=progress,
                    current_step=f"处理内容: {content.title[:30]}..."
                )
                
                # 执行内容改写和质检工作流
                workflow_task = {
                    "original_content": {
                        "title": content.original_title,
                        "content": content.original_content,
                        "url": content.source_url
                    },
                    "content_id": content.id
                }
                
                import asyncio
                workflow_result = asyncio.run(
                    agent_manager.execute_workflow("content_only", workflow_task)
                )
                
                if workflow_result.get("success"):
                    # 更新内容
                    rewrite_data = None
                    quality_data = None
                    
                    for step in workflow_result.get("steps", []):
                        if step["agent_name"] == "content_rewrite":
                            rewrite_data = step["result"].get("data", {})
                        elif step["agent_name"] == "quality_control":
                            quality_data = step["result"].get("data", {})
                    
                    if rewrite_data:
                        content.title = rewrite_data.get("rewritten_title", content.title)
                        content.content = rewrite_data.get("rewritten_content", content.content)
                        content.summary = rewrite_data.get("summary", content.summary)
                        content.tags = rewrite_data.get("tags", [])
                        content.keywords = rewrite_data.get("keywords", [])
                    
                    if quality_data:
                        content.quality_score = int(quality_data.get("overall_score", 0) * 10)
                        content.readability_score = int(quality_data.get("detailed_scores", {}).get("readability", 0) * 10)
                        content.originality_score = int(quality_data.get("detailed_scores", {}).get("originality", 0) * 10)
                        content.is_approved = quality_data.get("is_approved", False)
                    
                    content.is_processed = True
                    processed_count += 1
                
            except Exception as e:
                logger.error(f"处理内容 {content.id} 失败: {str(e)}")
                continue
        
        db.commit()
        db.close()
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
        
        return {
            "success": True,
            "total_contents": total_contents,
            "processed_count": processed_count,
            "success_rate": processed_count / total_contents if total_contents > 0 else 0
        }
        
    except Exception as e:
        logger.error(f"内容处理任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


def update_task_status(task_id: str, status: TaskStatus, progress: int = 0, 
                      current_step: str = None, error_message: str = None):
    """更新任务状态"""
    try:
        db = SessionLocal()
        
        task_record = db.query(Task).filter(Task.task_id == task_id).first()
        if not task_record:
            # 创建新的任务记录
            task_record = Task(
                task_id=task_id,
                task_type="crawl_news",  # 默认类型
                status=status
            )
            db.add(task_record)
        else:
            task_record.status = status
        
        task_record.progress = progress
        if current_step:
            task_record.current_step = current_step
        if error_message:
            task_record.error_message = error_message
        
        db.commit()
        db.close()
        
        # 更新Celery任务状态
        if current_task:
            current_task.update_state(
                state=status.value.upper(),
                meta={
                    "progress": progress,
                    "current_step": current_step,
                    "error": error_message
                }
            )
            
    except Exception as e:
        logger.error(f"更新任务状态失败: {str(e)}")
