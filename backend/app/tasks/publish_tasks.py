"""
发布相关的Celery任务
"""
from celery import current_task
from app.core.celery_app import task
from app.agents.agent_manager import agent_manager
from app.db.base import SessionLocal
from app.models import Content, Publication, Task, TaskStatus, PublicationStatus
from app.tasks.news_tasks import update_task_status
from typing import Dict, Any, List, Optional
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@task(bind=True, name="publish_content")
def publish_content(self, content_id: int, platform: str = "wechat", 
                   publish_strategy: str = "immediate",
                   scheduled_time: Optional[str] = None) -> Dict[str, Any]:
    """发布单个内容"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=10, current_step="获取内容")
        
        db = SessionLocal()
        
        # 获取内容
        content = db.query(Content).filter(Content.id == content_id).first()
        if not content:
            raise ValueError(f"内容不存在: {content_id}")
        
        # 检查内容是否已审核通过
        if not content.is_approved:
            raise ValueError(f"内容未通过审核，无法发布")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=30, current_step="准备发布任务")
        
        # 准备发布任务
        publish_task = {
            "content": {
                "id": content.id,
                "title": content.title,
                "content": content.content,
                "summary": content.summary,
                "tags": content.tags or [],
                "keywords": content.keywords or []
            },
            "platform": platform,
            "publish_strategy": publish_strategy,
            "scheduled_time": scheduled_time,
            "publish_config": {
                "source_attribution": content.source_name,
                "category": content.category
            }
        }
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=50, current_step="执行发布")
        
        # 执行发布
        import asyncio
        result = asyncio.run(agent_manager.execute_single_agent("publish", publish_task))
        
        if not result.get("success"):
            raise Exception(f"发布失败: {result.get('error')}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=80, current_step="保存发布记录")
        
        # 保存发布记录
        publish_data = result.get("data", {})
        publish_result = publish_data.get("publish_result", {})
        
        publication = Publication(
            content_id=content_id,
            platform=platform,
            platform_post_id=publish_result.get("platform_post_id"),
            publish_url=publish_result.get("publish_url"),
            status=PublicationStatus.PUBLISHED if publish_result.get("status") == "success" else PublicationStatus.FAILED,
            scheduled_time=datetime.fromisoformat(scheduled_time) if scheduled_time else None,
            published_time=datetime.fromisoformat(publish_result.get("publish_time")) if publish_result.get("publish_time") else datetime.now(),
            metadata={
                "publish_strategy": publish_strategy,
                "platform_response": publish_result.get("platform_response", {}),
                "post_processing": publish_data.get("post_processing", {})
            }
        )
        
        db.add(publication)
        
        # 更新内容状态
        content.is_published = True
        content.published_count += 1
        
        db.commit()
        db.close()
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "content_id": content_id,
            "platform": platform,
            "publication_id": publication.id,
            "publish_result": publish_result
        }
        
    except Exception as e:
        logger.error(f"发布任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="batch_publish_content")
def batch_publish_content(self, content_ids: List[int], platform: str = "wechat",
                         publish_strategy: str = "optimal_time") -> Dict[str, Any]:
    """批量发布内容"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=5, current_step="初始化批量发布")
        
        total_contents = len(content_ids)
        publish_results = []
        
        for i, content_id in enumerate(content_ids):
            try:
                progress = 10 + (i * 80 // total_contents)
                update_task_status(
                    task_id,
                    TaskStatus.RUNNING,
                    progress=progress,
                    current_step=f"发布内容 {i+1}/{total_contents}"
                )
                
                # 为批量发布计算发布时间间隔
                if publish_strategy == "optimal_time" and i > 0:
                    # 间隔30分钟发布，避免频繁发布
                    scheduled_time = (datetime.now() + timedelta(minutes=30 * i)).isoformat()
                else:
                    scheduled_time = None
                
                publish_result = publish_content.apply_async(
                    args=[content_id, platform, publish_strategy, scheduled_time]
                ).get(timeout=300)  # 5分钟超时
                
                publish_results.append({
                    "content_id": content_id,
                    "result": publish_result
                })
                
            except Exception as e:
                logger.error(f"发布内容 {content_id} 失败: {str(e)}")
                publish_results.append({
                    "content_id": content_id,
                    "result": {"success": False, "error": str(e)}
                })
        
        # 统计结果
        successful_count = sum(1 for r in publish_results if r["result"].get("success"))
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "total_contents": total_contents,
            "successful_count": successful_count,
            "success_rate": successful_count / total_contents if total_contents > 0 else 0,
            "results": publish_results
        }
        
    except Exception as e:
        logger.error(f"批量发布任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="auto_publish_approved_content")
def auto_publish_approved_content(self, platform: str = "wechat", 
                                 max_daily_posts: int = 5) -> Dict[str, Any]:
    """自动发布已审核通过的内容"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=10, current_step="查找待发布内容")
        
        db = SessionLocal()
        
        # 查找今天已发布的数量
        today = datetime.now().date()
        today_published = db.query(Publication).filter(
            Publication.published_time >= today,
            Publication.platform == platform,
            Publication.status == PublicationStatus.PUBLISHED
        ).count()
        
        if today_published >= max_daily_posts:
            db.close()
            update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
            return {
                "success": True,
                "message": f"今日已达到最大发布数量限制: {max_daily_posts}"
            }
        
        # 查找待发布的内容
        remaining_slots = max_daily_posts - today_published
        
        approved_contents = db.query(Content).filter(
            Content.is_approved == True,
            Content.is_published == False,
            Content.quality_score >= 70  # 质量分数阈值
        ).order_by(Content.quality_score.desc()).limit(remaining_slots).all()
        
        if not approved_contents:
            db.close()
            update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
            return {"success": True, "message": "没有待发布的优质内容"}
        
        content_ids = [content.id for content in approved_contents]
        db.close()
        
        update_task_status(
            task_id,
            TaskStatus.RUNNING,
            progress=30,
            current_step=f"找到 {len(content_ids)} 个待发布内容"
        )
        
        # 执行批量发布
        batch_result = batch_publish_content.apply_async(
            args=[content_ids, platform, "optimal_time"]
        ).get(timeout=1800)  # 30分钟超时
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100)
        
        return {
            "success": True,
            "today_published_before": today_published,
            "new_published_count": len(content_ids),
            "batch_result": batch_result
        }
        
    except Exception as e:
        logger.error(f"自动发布任务失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="schedule_content_publication")
def schedule_content_publication(self, content_id: int, platform: str,
                                scheduled_time: str) -> Dict[str, Any]:
    """安排内容定时发布"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=20, current_step="验证发布时间")
        
        # 验证发布时间
        try:
            publish_datetime = datetime.fromisoformat(scheduled_time)
            if publish_datetime <= datetime.now():
                raise ValueError("发布时间必须是未来时间")
        except ValueError as e:
            raise ValueError(f"无效的发布时间格式: {str(e)}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=50, current_step="创建定时任务")
        
        # 创建定时发布任务
        from app.core.celery_app import celery_app
        
        eta = publish_datetime
        scheduled_task = publish_content.apply_async(
            args=[content_id, platform, "scheduled", scheduled_time],
            eta=eta
        )
        
        # 记录定时任务
        db = SessionLocal()
        
        content = db.query(Content).filter(Content.id == content_id).first()
        if not content:
            raise ValueError(f"内容不存在: {content_id}")
        
        publication = Publication(
            content_id=content_id,
            platform=platform,
            status=PublicationStatus.SCHEDULED,
            scheduled_time=publish_datetime,
            metadata={
                "celery_task_id": scheduled_task.id,
                "publish_strategy": "scheduled"
            }
        )
        
        db.add(publication)
        db.commit()
        db.close()
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "content_id": content_id,
            "platform": platform,
            "scheduled_time": scheduled_time,
            "celery_task_id": scheduled_task.id,
            "publication_id": publication.id
        }
        
    except Exception as e:
        logger.error(f"定时发布安排失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="cancel_scheduled_publication")
def cancel_scheduled_publication(self, publication_id: int) -> Dict[str, Any]:
    """取消定时发布"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=30, current_step="查找发布记录")
        
        db = SessionLocal()
        
        publication = db.query(Publication).filter(Publication.id == publication_id).first()
        if not publication:
            raise ValueError(f"发布记录不存在: {publication_id}")
        
        if publication.status != PublicationStatus.SCHEDULED:
            raise ValueError(f"发布记录状态不是已安排: {publication.status}")
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=60, current_step="取消Celery任务")
        
        # 取消Celery任务
        celery_task_id = publication.metadata.get("celery_task_id")
        if celery_task_id:
            from app.core.celery_app import celery_app
            celery_app.control.revoke(celery_task_id, terminate=True)
        
        # 更新发布记录状态
        publication.status = PublicationStatus.CANCELLED
        publication.metadata["cancelled_time"] = datetime.now().isoformat()
        
        db.commit()
        db.close()
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "publication_id": publication_id,
            "cancelled_task_id": celery_task_id
        }
        
    except Exception as e:
        logger.error(f"取消定时发布失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }


@task(bind=True, name="generate_publication_report")
def generate_publication_report(self, platform: Optional[str] = None,
                               date_range_days: int = 30) -> Dict[str, Any]:
    """生成发布报告"""
    task_id = self.request.id
    
    try:
        update_task_status(task_id, TaskStatus.RUNNING, progress=20, current_step="收集发布数据")
        
        db = SessionLocal()
        
        from sqlalchemy import func
        
        cutoff_date = datetime.now() - timedelta(days=date_range_days)
        
        # 基础查询
        query = db.query(Publication).filter(Publication.published_time >= cutoff_date)
        if platform:
            query = query.filter(Publication.platform == platform)
        
        # 统计数据
        total_publications = query.count()
        successful_publications = query.filter(Publication.status == PublicationStatus.PUBLISHED).count()
        failed_publications = query.filter(Publication.status == PublicationStatus.FAILED).count()
        scheduled_publications = query.filter(Publication.status == PublicationStatus.SCHEDULED).count()
        
        # 按平台统计
        platform_stats = db.query(
            Publication.platform,
            func.count(Publication.id).label('total'),
            func.sum(func.case([(Publication.status == PublicationStatus.PUBLISHED, 1)], else_=0)).label('successful')
        ).filter(
            Publication.published_time >= cutoff_date
        ).group_by(Publication.platform).all()
        
        # 按日期统计
        daily_stats = db.query(
            func.date(Publication.published_time).label('date'),
            func.count(Publication.id).label('count')
        ).filter(
            Publication.published_time >= cutoff_date,
            Publication.status == PublicationStatus.PUBLISHED
        ).group_by(func.date(Publication.published_time)).all()
        
        db.close()
        
        update_task_status(task_id, TaskStatus.RUNNING, progress=80, current_step="生成报告")
        
        report = {
            "period": f"最近 {date_range_days} 天",
            "platform_filter": platform or "全部平台",
            "summary": {
                "total_publications": total_publications,
                "successful_publications": successful_publications,
                "failed_publications": failed_publications,
                "scheduled_publications": scheduled_publications,
                "success_rate": successful_publications / total_publications if total_publications > 0 else 0
            },
            "platform_statistics": [
                {
                    "platform": stat.platform,
                    "total_publications": stat.total,
                    "successful_publications": stat.successful,
                    "success_rate": stat.successful / stat.total if stat.total > 0 else 0
                }
                for stat in platform_stats
            ],
            "daily_statistics": [
                {
                    "date": stat.date.isoformat(),
                    "publications_count": stat.count
                }
                for stat in daily_stats
            ]
        }
        
        update_task_status(task_id, TaskStatus.SUCCESS, progress=100, current_step="完成")
        
        return {
            "success": True,
            "report": report
        }
        
    except Exception as e:
        logger.error(f"生成发布报告失败: {str(e)}")
        update_task_status(task_id, TaskStatus.FAILED, error_message=str(e))
        return {
            "success": False,
            "error": str(e)
        }
