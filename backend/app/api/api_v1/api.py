"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import auth, users, news_sources, contents, tasks, publications

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(news_sources.router, prefix="/news-sources", tags=["资讯源管理"])
api_router.include_router(contents.router, prefix="/contents", tags=["内容管理"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(publications.router, prefix="/publications", tags=["发布管理"])
