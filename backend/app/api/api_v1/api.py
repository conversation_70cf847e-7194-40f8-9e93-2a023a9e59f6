"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import auth, users, news_sources, contents, tasks, publications
# 暂时注释掉智能体相关导入，避免启动问题
# from app.api import agents, tasks as task_management

api_router = APIRouter()

# 注册各模块路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(news_sources.router, prefix="/news-sources", tags=["资讯源管理"])
api_router.include_router(contents.router, prefix="/contents", tags=["内容管理"])
api_router.include_router(tasks.router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(publications.router, prefix="/publications", tags=["发布管理"])

# 暂时注释掉智能体相关路由，避免启动问题
# api_router.include_router(agents.router, prefix="/ai-agents", tags=["AI智能体"])
# api_router.include_router(task_management.router, prefix="/task-management", tags=["任务监控"])
