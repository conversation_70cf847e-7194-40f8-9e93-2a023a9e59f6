"""
认证相关API端点
"""

from fastapi import APIRouter, HTTPException, Depends
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel
from typing import Optional

router = APIRouter()
security = HTTPBearer()

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_id: str
    username: str

class UserInfo(BaseModel):
    user_id: str
    username: str
    email: Optional[str] = None
    is_active: bool = True

@router.post("/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    # 简单的演示登录逻辑
    if request.username == "admin" and request.password == "admin123":
        return LoginResponse(
            access_token="demo_token_12345",
            user_id="user_001",
            username=request.username
        )
    raise HTTPException(status_code=401, detail="用户名或密码错误")

@router.post("/logout")
async def logout():
    """用户登出"""
    return {"message": "登出成功"}

@router.get("/me", response_model=UserInfo)
async def get_current_user():
    """获取当前用户信息"""
    return UserInfo(
        user_id="user_001",
        username="admin",
        email="<EMAIL>"
    )

@router.post("/refresh")
async def refresh_token():
    """刷新访问令牌"""
    return {
        "access_token": "new_demo_token_67890",
        "token_type": "bearer"
    }
