"""
发布管理API端点
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

router = APIRouter()

class PublicationPlatform(str, Enum):
    WECHAT = "wechat"
    WEIBO = "weibo"
    TOUTIAO = "toutiao"
    ZHIHU = "zhihu"

class PublicationStatus(str, Enum):
    DRAFT = "draft"
    SCHEDULED = "scheduled"
    PUBLISHING = "publishing"
    PUBLISHED = "published"
    FAILED = "failed"

class Publication(BaseModel):
    id: str
    title: str
    content: str
    platform: PublicationPlatform
    status: PublicationStatus
    content_id: str
    scheduled_at: Optional[datetime] = None
    published_at: Optional[datetime] = None
    platform_post_id: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    created_by: str

class CreatePublicationRequest(BaseModel):
    title: str
    content: str
    platform: PublicationPlatform
    content_id: str
    scheduled_at: Optional[datetime] = None

class UpdatePublicationRequest(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    scheduled_at: Optional[datetime] = None
    status: Optional[PublicationStatus] = None

class PublicationMetrics(BaseModel):
    views: int = 0
    likes: int = 0
    shares: int = 0
    comments: int = 0
    engagement_rate: float = 0.0

# 演示数据
demo_publications = [
    Publication(
        id="pub_001",
        title="AI技术在新闻行业的应用前景",
        content="AI正在重塑新闻业：从自动化写作到个性化推荐...",
        platform=PublicationPlatform.WECHAT,
        status=PublicationStatus.PUBLISHED,
        content_id="content_001",
        published_at=datetime.now(),
        platform_post_id="wx_post_123456",
        metrics={"views": 1250, "likes": 89, "shares": 23, "comments": 15},
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="admin"
    ),
    Publication(
        id="pub_002",
        title="2024年科技趋势预测",
        content="2024年将是科技发展的关键一年...",
        platform=PublicationPlatform.WEIBO,
        status=PublicationStatus.SCHEDULED,
        content_id="content_002",
        scheduled_at=datetime.now(),
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="editor"
    )
]

@router.get("/", response_model=List[Publication])
async def get_publications(
    platform: Optional[PublicationPlatform] = Query(None, description="按平台筛选"),
    status: Optional[PublicationStatus] = Query(None, description="按状态筛选"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """获取发布记录列表"""
    filtered_publications = demo_publications
    
    if platform:
        filtered_publications = [p for p in filtered_publications if p.platform == platform]
    
    if status:
        filtered_publications = [p for p in filtered_publications if p.status == status]
    
    return filtered_publications[:limit]

@router.get("/{publication_id}", response_model=Publication)
async def get_publication(publication_id: str):
    """获取指定发布记录详情"""
    for publication in demo_publications:
        if publication.id == publication_id:
            return publication
    raise HTTPException(status_code=404, detail="发布记录不存在")

@router.post("/", response_model=Publication)
async def create_publication(request: CreatePublicationRequest):
    """创建新发布记录"""
    new_publication = Publication(
        id=f"pub_{len(demo_publications) + 1:03d}",
        title=request.title,
        content=request.content,
        platform=request.platform,
        status=PublicationStatus.DRAFT if not request.scheduled_at else PublicationStatus.SCHEDULED,
        content_id=request.content_id,
        scheduled_at=request.scheduled_at,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        created_by="admin"
    )
    demo_publications.append(new_publication)
    return new_publication

@router.put("/{publication_id}", response_model=Publication)
async def update_publication(publication_id: str, request: UpdatePublicationRequest):
    """更新发布记录"""
    for i, publication in enumerate(demo_publications):
        if publication.id == publication_id:
            if request.title is not None:
                demo_publications[i].title = request.title
            if request.content is not None:
                demo_publications[i].content = request.content
            if request.scheduled_at is not None:
                demo_publications[i].scheduled_at = request.scheduled_at
            if request.status is not None:
                demo_publications[i].status = request.status
            demo_publications[i].updated_at = datetime.now()
            return demo_publications[i]
    raise HTTPException(status_code=404, detail="发布记录不存在")

@router.post("/{publication_id}/publish")
async def publish_now(publication_id: str):
    """立即发布"""
    for i, publication in enumerate(demo_publications):
        if publication.id == publication_id:
            if publication.status not in [PublicationStatus.DRAFT, PublicationStatus.SCHEDULED]:
                raise HTTPException(status_code=400, detail="只能发布草稿或计划发布的内容")
            demo_publications[i].status = PublicationStatus.PUBLISHING
            return {
                "message": f"开始发布到 {publication.platform.value}",
                "publication_id": publication_id
            }
    raise HTTPException(status_code=404, detail="发布记录不存在")

@router.get("/{publication_id}/metrics", response_model=PublicationMetrics)
async def get_publication_metrics(publication_id: str):
    """获取发布数据统计"""
    for publication in demo_publications:
        if publication.id == publication_id:
            if publication.metrics:
                return PublicationMetrics(**publication.metrics)
            return PublicationMetrics()
    raise HTTPException(status_code=404, detail="发布记录不存在")

@router.delete("/{publication_id}")
async def delete_publication(publication_id: str):
    """删除发布记录"""
    for i, publication in enumerate(demo_publications):
        if publication.id == publication_id:
            if publication.status == PublicationStatus.PUBLISHING:
                raise HTTPException(status_code=400, detail="不能删除正在发布的记录")
            demo_publications.pop(i)
            return {"message": "发布记录删除成功"}
    raise HTTPException(status_code=404, detail="发布记录不存在")
