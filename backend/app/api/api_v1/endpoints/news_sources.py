"""
资讯源管理API端点
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, HttpUrl
from typing import List, Optional
from datetime import datetime
from enum import Enum

router = APIRouter()

class SourceType(str, Enum):
    RSS = "rss"
    WEB = "web"
    API = "api"

class SourceStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

class NewsSource(BaseModel):
    id: str
    name: str
    url: HttpUrl
    source_type: SourceType
    status: SourceStatus
    description: Optional[str] = None
    last_crawled: Optional[datetime] = None
    article_count: int = 0
    created_at: datetime

class CreateSourceRequest(BaseModel):
    name: str
    url: HttpUrl
    source_type: SourceType
    description: Optional[str] = None

class UpdateSourceRequest(BaseModel):
    name: Optional[str] = None
    url: Optional[HttpUrl] = None
    description: Optional[str] = None
    status: Optional[SourceStatus] = None

# 演示数据
demo_sources = [
    NewsSource(
        id="source_001",
        name="36氪",
        url="https://36kr.com/feed",
        source_type=SourceType.RSS,
        status=SourceStatus.ACTIVE,
        description="科技创业资讯",
        last_crawled=datetime.now(),
        article_count=156,
        created_at=datetime.now()
    ),
    NewsSource(
        id="source_002",
        name="虎嗅网",
        url="https://www.huxiu.com/rss/0.xml",
        source_type=SourceType.RSS,
        status=SourceStatus.ACTIVE,
        description="商业科技资讯",
        last_crawled=datetime.now(),
        article_count=89,
        created_at=datetime.now()
    )
]

@router.get("/", response_model=List[NewsSource])
async def get_news_sources():
    """获取资讯源列表"""
    return demo_sources

@router.get("/{source_id}", response_model=NewsSource)
async def get_news_source(source_id: str):
    """获取指定资讯源信息"""
    for source in demo_sources:
        if source.id == source_id:
            return source
    raise HTTPException(status_code=404, detail="资讯源不存在")

@router.post("/", response_model=NewsSource)
async def create_news_source(request: CreateSourceRequest):
    """创建新资讯源"""
    new_source = NewsSource(
        id=f"source_{len(demo_sources) + 1:03d}",
        name=request.name,
        url=request.url,
        source_type=request.source_type,
        status=SourceStatus.ACTIVE,
        description=request.description,
        article_count=0,
        created_at=datetime.now()
    )
    demo_sources.append(new_source)
    return new_source

@router.put("/{source_id}", response_model=NewsSource)
async def update_news_source(source_id: str, request: UpdateSourceRequest):
    """更新资讯源信息"""
    for i, source in enumerate(demo_sources):
        if source.id == source_id:
            if request.name is not None:
                demo_sources[i].name = request.name
            if request.url is not None:
                demo_sources[i].url = request.url
            if request.description is not None:
                demo_sources[i].description = request.description
            if request.status is not None:
                demo_sources[i].status = request.status
            return demo_sources[i]
    raise HTTPException(status_code=404, detail="资讯源不存在")

@router.delete("/{source_id}")
async def delete_news_source(source_id: str):
    """删除资讯源"""
    for i, source in enumerate(demo_sources):
        if source.id == source_id:
            demo_sources.pop(i)
            return {"message": "资讯源删除成功"}
    raise HTTPException(status_code=404, detail="资讯源不存在")

@router.post("/{source_id}/crawl")
async def crawl_news_source(source_id: str):
    """手动爬取指定资讯源"""
    for source in demo_sources:
        if source.id == source_id:
            return {
                "message": f"开始爬取资讯源: {source.name}",
                "source_id": source_id,
                "status": "started"
            }
    raise HTTPException(status_code=404, detail="资讯源不存在")
