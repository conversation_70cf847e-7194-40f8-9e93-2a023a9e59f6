"""
任务管理API端点
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

router = APIRouter()

class TaskType(str, Enum):
    CRAWL = "crawl"
    REWRITE = "rewrite"
    PUBLISH = "publish"
    ANALYZE = "analyze"

class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class Task(BaseModel):
    id: str
    name: str
    task_type: TaskType
    status: TaskStatus
    progress: int = 0  # 0-100
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_by: str

class CreateTaskRequest(BaseModel):
    name: str
    task_type: TaskType
    parameters: Optional[Dict[str, Any]] = None

class TaskResult(BaseModel):
    task_id: str
    status: TaskStatus
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None

# 演示数据
demo_tasks = [
    Task(
        id="task_001",
        name="爬取36氪最新资讯",
        task_type=TaskType.CRAWL,
        status=TaskStatus.COMPLETED,
        progress=100,
        result={"articles_count": 25, "success_count": 23},
        created_at=datetime.now(),
        started_at=datetime.now(),
        completed_at=datetime.now(),
        created_by="admin"
    ),
    Task(
        id="task_002",
        name="AI改写科技文章",
        task_type=TaskType.REWRITE,
        status=TaskStatus.RUNNING,
        progress=65,
        created_at=datetime.now(),
        started_at=datetime.now(),
        created_by="admin"
    ),
    Task(
        id="task_003",
        name="发布到微信公众号",
        task_type=TaskType.PUBLISH,
        status=TaskStatus.PENDING,
        progress=0,
        created_at=datetime.now(),
        created_by="editor"
    )
]

@router.get("/", response_model=List[Task])
async def get_tasks(
    status: Optional[TaskStatus] = Query(None, description="按状态筛选"),
    task_type: Optional[TaskType] = Query(None, description="按类型筛选"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """获取任务列表"""
    filtered_tasks = demo_tasks
    
    if status:
        filtered_tasks = [t for t in filtered_tasks if t.status == status]
    
    if task_type:
        filtered_tasks = [t for t in filtered_tasks if t.task_type == task_type]
    
    return filtered_tasks[:limit]

@router.get("/{task_id}", response_model=Task)
async def get_task(task_id: str):
    """获取指定任务详情"""
    for task in demo_tasks:
        if task.id == task_id:
            return task
    raise HTTPException(status_code=404, detail="任务不存在")

@router.post("/", response_model=Task)
async def create_task(request: CreateTaskRequest):
    """创建新任务"""
    new_task = Task(
        id=f"task_{len(demo_tasks) + 1:03d}",
        name=request.name,
        task_type=request.task_type,
        status=TaskStatus.PENDING,
        progress=0,
        created_at=datetime.now(),
        created_by="admin"
    )
    demo_tasks.append(new_task)
    return new_task

@router.post("/{task_id}/start")
async def start_task(task_id: str):
    """启动任务"""
    for i, task in enumerate(demo_tasks):
        if task.id == task_id:
            if task.status != TaskStatus.PENDING:
                raise HTTPException(status_code=400, detail="只能启动待执行的任务")
            demo_tasks[i].status = TaskStatus.RUNNING
            demo_tasks[i].started_at = datetime.now()
            return {"message": f"任务 {task.name} 已启动", "task_id": task_id}
    raise HTTPException(status_code=404, detail="任务不存在")

@router.post("/{task_id}/cancel")
async def cancel_task(task_id: str):
    """取消任务"""
    for i, task in enumerate(demo_tasks):
        if task.id == task_id:
            if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                raise HTTPException(status_code=400, detail="只能取消待执行或运行中的任务")
            demo_tasks[i].status = TaskStatus.CANCELLED
            return {"message": f"任务 {task.name} 已取消", "task_id": task_id}
    raise HTTPException(status_code=404, detail="任务不存在")

@router.get("/{task_id}/result", response_model=TaskResult)
async def get_task_result(task_id: str):
    """获取任务结果"""
    for task in demo_tasks:
        if task.id == task_id:
            return TaskResult(
                task_id=task.id,
                status=task.status,
                result=task.result,
                error_message=task.error_message
            )
    raise HTTPException(status_code=404, detail="任务不存在")

@router.delete("/{task_id}")
async def delete_task(task_id: str):
    """删除任务"""
    for i, task in enumerate(demo_tasks):
        if task.id == task_id:
            if task.status == TaskStatus.RUNNING:
                raise HTTPException(status_code=400, detail="不能删除运行中的任务")
            demo_tasks.pop(i)
            return {"message": "任务删除成功"}
    raise HTTPException(status_code=404, detail="任务不存在")
