"""
内容管理API端点
"""

from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
from enum import Enum

router = APIRouter()

class ContentStatus(str, Enum):
    DRAFT = "draft"
    PROCESSING = "processing"
    READY = "ready"
    PUBLISHED = "published"
    ARCHIVED = "archived"

class ContentType(str, Enum):
    ARTICLE = "article"
    NEWS = "news"
    REPORT = "report"

class Content(BaseModel):
    id: str
    title: str
    original_content: str
    rewritten_content: Optional[str] = None
    summary: Optional[str] = None
    keywords: List[str] = []
    content_type: ContentType
    status: ContentStatus
    source_id: str
    source_name: str
    original_url: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    published_at: Optional[datetime] = None

class CreateContentRequest(BaseModel):
    title: str
    original_content: str
    content_type: ContentType
    source_id: str
    original_url: Optional[str] = None

class UpdateContentRequest(BaseModel):
    title: Optional[str] = None
    rewritten_content: Optional[str] = None
    summary: Optional[str] = None
    keywords: Optional[List[str]] = None
    status: Optional[ContentStatus] = None

# 演示数据
demo_contents = [
    Content(
        id="content_001",
        title="AI技术在新闻行业的应用前景",
        original_content="人工智能技术正在革命性地改变新闻行业...",
        rewritten_content="AI正在重塑新闻业：从自动化写作到个性化推荐...",
        summary="探讨AI技术如何改变新闻行业的工作流程和用户体验",
        keywords=["AI", "新闻", "技术", "自动化"],
        content_type=ContentType.ARTICLE,
        status=ContentStatus.READY,
        source_id="source_001",
        source_name="36氪",
        original_url="https://36kr.com/p/123456",
        created_at=datetime.now(),
        updated_at=datetime.now()
    ),
    Content(
        id="content_002",
        title="2024年科技趋势预测",
        original_content="2024年将是科技发展的关键一年...",
        summary="分析2024年最值得关注的科技发展趋势",
        keywords=["科技", "趋势", "2024", "预测"],
        content_type=ContentType.REPORT,
        status=ContentStatus.PROCESSING,
        source_id="source_002",
        source_name="虎嗅网",
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
]

@router.get("/", response_model=List[Content])
async def get_contents(
    status: Optional[ContentStatus] = Query(None, description="按状态筛选"),
    content_type: Optional[ContentType] = Query(None, description="按类型筛选"),
    limit: int = Query(20, ge=1, le=100, description="返回数量限制")
):
    """获取内容列表"""
    filtered_contents = demo_contents
    
    if status:
        filtered_contents = [c for c in filtered_contents if c.status == status]
    
    if content_type:
        filtered_contents = [c for c in filtered_contents if c.content_type == content_type]
    
    return filtered_contents[:limit]

@router.get("/{content_id}", response_model=Content)
async def get_content(content_id: str):
    """获取指定内容详情"""
    for content in demo_contents:
        if content.id == content_id:
            return content
    raise HTTPException(status_code=404, detail="内容不存在")

@router.post("/", response_model=Content)
async def create_content(request: CreateContentRequest):
    """创建新内容"""
    new_content = Content(
        id=f"content_{len(demo_contents) + 1:03d}",
        title=request.title,
        original_content=request.original_content,
        content_type=request.content_type,
        status=ContentStatus.DRAFT,
        source_id=request.source_id,
        source_name="演示资讯源",
        original_url=request.original_url,
        created_at=datetime.now(),
        updated_at=datetime.now()
    )
    demo_contents.append(new_content)
    return new_content

@router.put("/{content_id}", response_model=Content)
async def update_content(content_id: str, request: UpdateContentRequest):
    """更新内容信息"""
    for i, content in enumerate(demo_contents):
        if content.id == content_id:
            if request.title is not None:
                demo_contents[i].title = request.title
            if request.rewritten_content is not None:
                demo_contents[i].rewritten_content = request.rewritten_content
            if request.summary is not None:
                demo_contents[i].summary = request.summary
            if request.keywords is not None:
                demo_contents[i].keywords = request.keywords
            if request.status is not None:
                demo_contents[i].status = request.status
            demo_contents[i].updated_at = datetime.now()
            return demo_contents[i]
    raise HTTPException(status_code=404, detail="内容不存在")

@router.delete("/{content_id}")
async def delete_content(content_id: str):
    """删除内容"""
    for i, content in enumerate(demo_contents):
        if content.id == content_id:
            demo_contents.pop(i)
            return {"message": "内容删除成功"}
    raise HTTPException(status_code=404, detail="内容不存在")

@router.post("/{content_id}/rewrite")
async def rewrite_content(content_id: str):
    """AI改写内容"""
    for content in demo_contents:
        if content.id == content_id:
            return {
                "message": f"开始AI改写内容: {content.title}",
                "content_id": content_id,
                "status": "started"
            }
    raise HTTPException(status_code=404, detail="内容不存在")
