"""
用户管理API端点
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

router = APIRouter()

class User(BaseModel):
    id: str
    username: str
    email: Optional[str] = None
    is_active: bool = True
    created_at: datetime
    last_login: Optional[datetime] = None

class CreateUserRequest(BaseModel):
    username: str
    email: str
    password: str

class UpdateUserRequest(BaseModel):
    email: Optional[str] = None
    is_active: Optional[bool] = None

# 演示数据
demo_users = [
    User(
        id="user_001",
        username="admin",
        email="<EMAIL>",
        is_active=True,
        created_at=datetime.now(),
        last_login=datetime.now()
    ),
    User(
        id="user_002", 
        username="editor",
        email="<EMAIL>",
        is_active=True,
        created_at=datetime.now()
    )
]

@router.get("/", response_model=List[User])
async def get_users():
    """获取用户列表"""
    return demo_users

@router.get("/{user_id}", response_model=User)
async def get_user(user_id: str):
    """获取指定用户信息"""
    for user in demo_users:
        if user.id == user_id:
            return user
    raise HTTPException(status_code=404, detail="用户不存在")

@router.post("/", response_model=User)
async def create_user(request: CreateUserRequest):
    """创建新用户"""
    new_user = User(
        id=f"user_{len(demo_users) + 1:03d}",
        username=request.username,
        email=request.email,
        is_active=True,
        created_at=datetime.now()
    )
    demo_users.append(new_user)
    return new_user

@router.put("/{user_id}", response_model=User)
async def update_user(user_id: str, request: UpdateUserRequest):
    """更新用户信息"""
    for i, user in enumerate(demo_users):
        if user.id == user_id:
            if request.email is not None:
                demo_users[i].email = request.email
            if request.is_active is not None:
                demo_users[i].is_active = request.is_active
            return demo_users[i]
    raise HTTPException(status_code=404, detail="用户不存在")

@router.delete("/{user_id}")
async def delete_user(user_id: str):
    """删除用户"""
    for i, user in enumerate(demo_users):
        if user.id == user_id:
            demo_users.pop(i)
            return {"message": "用户删除成功"}
    raise HTTPException(status_code=404, detail="用户不存在")
