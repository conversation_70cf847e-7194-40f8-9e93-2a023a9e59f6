"""
智能体相关API
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import Dict, Any, List, Optional
from app.agents.agent_manager import agent_manager
from app.tasks.news_tasks import crawl_news_source, crawl_all_sources, process_crawled_content
from app.tasks.content_tasks import rewrite_content, quality_check_content, batch_process_content
from app.tasks.publish_tasks import publish_content, batch_publish_content
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


# 请求模型
class AgentExecuteRequest(BaseModel):
    agent_name: str
    task_data: Dict[str, Any]


class WorkflowExecuteRequest(BaseModel):
    workflow_name: str
    initial_task: Dict[str, Any]


class BatchAgentRequest(BaseModel):
    agent_name: str
    tasks: List[Dict[str, Any]]


class CrawlSourceRequest(BaseModel):
    source_id: int
    max_articles: int = 10


class CrawlAllSourcesRequest(BaseModel):
    max_articles_per_source: int = 10


class ProcessContentRequest(BaseModel):
    content_ids: List[int]


class RewriteContentRequest(BaseModel):
    content_id: int
    style: str = "wechat_official"
    target_length: str = "medium"


class QualityCheckRequest(BaseModel):
    content_id: int
    check_level: str = "standard"


class PublishContentRequest(BaseModel):
    content_id: int
    platform: str = "wechat"
    publish_strategy: str = "immediate"
    scheduled_time: Optional[str] = None


class BatchPublishRequest(BaseModel):
    content_ids: List[int]
    platform: str = "wechat"
    publish_strategy: str = "optimal_time"


# 智能体信息API
@router.get("/agents/info")
async def get_agents_info():
    """获取所有智能体信息"""
    try:
        return {
            "success": True,
            "data": agent_manager.get_agent_info()
        }
    except Exception as e:
        logger.error(f"获取智能体信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/agents/{agent_name}/info")
async def get_agent_info(agent_name: str):
    """获取单个智能体信息"""
    try:
        return {
            "success": True,
            "data": agent_manager.get_agent_info(agent_name)
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取智能体信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflows/info")
async def get_workflows_info():
    """获取所有工作流程信息"""
    try:
        return {
            "success": True,
            "data": agent_manager.get_workflow_info()
        }
    except Exception as e:
        logger.error(f"获取工作流程信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflows/{workflow_name}/info")
async def get_workflow_info(workflow_name: str):
    """获取单个工作流程信息"""
    try:
        return {
            "success": True,
            "data": agent_manager.get_workflow_info(workflow_name)
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"获取工作流程信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 智能体执行API
@router.post("/agents/execute")
async def execute_agent(request: AgentExecuteRequest):
    """执行单个智能体"""
    try:
        result = await agent_manager.execute_single_agent(
            request.agent_name, 
            request.task_data
        )
        return {
            "success": True,
            "data": result
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"执行智能体失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/agents/batch-execute")
async def batch_execute_agent(request: BatchAgentRequest):
    """批量执行智能体"""
    try:
        results = await agent_manager.batch_execute(
            request.agent_name,
            request.tasks
        )
        return {
            "success": True,
            "data": results
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"批量执行智能体失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/workflows/execute")
async def execute_workflow(request: WorkflowExecuteRequest):
    """执行工作流程"""
    try:
        result = await agent_manager.execute_workflow(
            request.workflow_name,
            request.initial_task
        )
        return {
            "success": True,
            "data": result
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"执行工作流程失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 异步任务API
@router.post("/tasks/crawl-source")
async def start_crawl_source_task(request: CrawlSourceRequest, background_tasks: BackgroundTasks):
    """启动爬取单个资讯源任务"""
    try:
        task = crawl_news_source.delay(request.source_id, request.max_articles)
        return {
            "success": True,
            "task_id": task.id,
            "message": "爬取任务已启动"
        }
    except Exception as e:
        logger.error(f"启动爬取任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/crawl-all-sources")
async def start_crawl_all_sources_task(request: CrawlAllSourcesRequest, background_tasks: BackgroundTasks):
    """启动爬取所有资讯源任务"""
    try:
        task = crawl_all_sources.delay(request.max_articles_per_source)
        return {
            "success": True,
            "task_id": task.id,
            "message": "批量爬取任务已启动"
        }
    except Exception as e:
        logger.error(f"启动批量爬取任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/process-content")
async def start_process_content_task(request: ProcessContentRequest, background_tasks: BackgroundTasks):
    """启动内容处理任务"""
    try:
        task = process_crawled_content.delay(request.content_ids)
        return {
            "success": True,
            "task_id": task.id,
            "message": "内容处理任务已启动"
        }
    except Exception as e:
        logger.error(f"启动内容处理任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/rewrite-content")
async def start_rewrite_content_task(request: RewriteContentRequest, background_tasks: BackgroundTasks):
    """启动内容改写任务"""
    try:
        task = rewrite_content.delay(
            request.content_id,
            request.style,
            request.target_length
        )
        return {
            "success": True,
            "task_id": task.id,
            "message": "内容改写任务已启动"
        }
    except Exception as e:
        logger.error(f"启动内容改写任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/quality-check")
async def start_quality_check_task(request: QualityCheckRequest, background_tasks: BackgroundTasks):
    """启动质量检查任务"""
    try:
        task = quality_check_content.delay(
            request.content_id,
            request.check_level
        )
        return {
            "success": True,
            "task_id": task.id,
            "message": "质量检查任务已启动"
        }
    except Exception as e:
        logger.error(f"启动质量检查任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/publish-content")
async def start_publish_content_task(request: PublishContentRequest, background_tasks: BackgroundTasks):
    """启动内容发布任务"""
    try:
        task = publish_content.delay(
            request.content_id,
            request.platform,
            request.publish_strategy,
            request.scheduled_time
        )
        return {
            "success": True,
            "task_id": task.id,
            "message": "内容发布任务已启动"
        }
    except Exception as e:
        logger.error(f"启动内容发布任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/batch-publish")
async def start_batch_publish_task(request: BatchPublishRequest, background_tasks: BackgroundTasks):
    """启动批量发布任务"""
    try:
        task = batch_publish_content.delay(
            request.content_ids,
            request.platform,
            request.publish_strategy
        )
        return {
            "success": True,
            "task_id": task.id,
            "message": "批量发布任务已启动"
        }
    except Exception as e:
        logger.error(f"启动批量发布任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 健康检查API
@router.get("/health")
async def health_check():
    """智能体系统健康检查"""
    try:
        health_status = await agent_manager.health_check()
        return {
            "success": True,
            "data": health_status
        }
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
