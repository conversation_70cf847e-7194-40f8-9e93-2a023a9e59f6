"""
数据库基础配置
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from motor.motor_asyncio import AsyncIOMotorClient
import redis.asyncio as redis
from app.core.config import settings

# PostgreSQL配置
engine = create_engine(
    settings.database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.debug
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# MongoDB配置
class MongoDB:
    client: AsyncIOMotorClient = None
    database = None

mongodb = MongoDB()

# Redis配置
class RedisDB:
    redis_client: redis.Redis = None

redis_db = RedisDB()


async def get_database():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def connect_to_mongo():
    """连接到MongoDB"""
    mongodb.client = AsyncIOMotorClient(settings.mongodb_url)
    mongodb.database = mongodb.client[settings.mongodb_db]


async def close_mongo_connection():
    """关闭MongoDB连接"""
    if mongodb.client:
        mongodb.client.close()


async def connect_to_redis():
    """连接到Redis"""
    redis_db.redis_client = redis.from_url(
        settings.redis_url,
        encoding="utf-8",
        decode_responses=True
    )


async def close_redis_connection():
    """关闭Redis连接"""
    if redis_db.redis_client:
        await redis_db.redis_client.close()
