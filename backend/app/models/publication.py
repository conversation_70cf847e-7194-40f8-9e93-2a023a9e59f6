"""
发布记录模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base
import enum


class PublicationStatus(enum.Enum):
    """发布状态枚举"""
    SCHEDULED = "scheduled"
    PUBLISHING = "publishing"
    PUBLISHED = "published"
    FAILED = "failed"
    CANCELLED = "cancelled"


class PlatformType(enum.Enum):
    """平台类型枚举"""
    WECHAT = "wechat"
    WEIBO = "weibo"
    TOUTIAO = "toutiao"
    ZHIHU = "zhihu"


class Publication(Base):
    """发布记录表"""
    __tablename__ = "publications"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=False, comment="发布标题")
    content = Column(Text, comment="发布内容")
    
    # 平台信息
    platform = Column(Enum(PlatformType), nullable=False, comment="发布平台")
    platform_account = Column(String(100), comment="平台账号")
    platform_post_id = Column(String(100), comment="平台文章ID")
    
    # 发布配置
    scheduled_time = Column(DateTime(timezone=True), comment="计划发布时间")
    publish_config = Column(JSON, comment="发布配置")
    
    # 状态信息
    status = Column(Enum(PublicationStatus), default=PublicationStatus.SCHEDULED, comment="发布状态")
    error_message = Column(Text, comment="错误信息")
    retry_count = Column(Integer, default=0, comment="重试次数")
    
    # 统计信息
    view_count = Column(Integer, default=0, comment="阅读量")
    like_count = Column(Integer, default=0, comment="点赞数")
    comment_count = Column(Integer, default=0, comment="评论数")
    share_count = Column(Integer, default=0, comment="分享数")
    
    # 外键关联
    content_id = Column(Integer, ForeignKey("contents.id"), comment="内容ID")
    
    # 时间信息
    published_at = Column(DateTime(timezone=True), comment="实际发布时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    content = relationship("Content", back_populates="publications")
    
    def __repr__(self):
        return f"<Publication(id={self.id}, platform='{self.platform}', status='{self.status}')>"
