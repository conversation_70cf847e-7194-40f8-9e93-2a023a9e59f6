"""
任务模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Enum
from sqlalchemy.sql import func
from app.db.base import Base
import enum


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskType(enum.Enum):
    """任务类型枚举"""
    CRAWL_NEWS = "crawl_news"
    REWRITE_CONTENT = "rewrite_content"
    QUALITY_CHECK = "quality_check"
    PUBLISH_CONTENT = "publish_content"
    BATCH_PROCESS = "batch_process"


class Task(Base):
    """任务记录表"""
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(100), unique=True, index=True, comment="Celery任务ID")
    task_type = Column(Enum(TaskType), nullable=False, comment="任务类型")
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, comment="任务状态")
    
    # 任务参数
    parameters = Column(JSON, comment="任务参数")
    result = Column(JSON, comment="任务结果")
    error_message = Column(Text, comment="错误信息")
    
    # 进度信息
    progress = Column(Integer, default=0, comment="进度百分比")
    current_step = Column(String(100), comment="当前步骤")
    total_steps = Column(Integer, comment="总步骤数")
    
    # 时间信息
    started_at = Column(DateTime(timezone=True), comment="开始时间")
    completed_at = Column(DateTime(timezone=True), comment="完成时间")
    duration = Column(Integer, comment="执行时长(秒)")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<Task(id={self.id}, type='{self.task_type}', status='{self.status}')>"
