"""
资讯源模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base


class NewsSource(Base):
    """资讯源配置表"""
    __tablename__ = "news_sources"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="资讯源名称")
    url = Column(String(500), nullable=False, comment="资讯源URL")
    source_type = Column(String(50), nullable=False, comment="资讯源类型")
    description = Column(Text, comment="描述")
    
    # 爬虫配置
    scraper_config = Column(JSON, comment="爬虫配置参数")
    headers = Column(JSON, comment="请求头配置")
    selectors = Column(JSON, comment="CSS选择器配置")
    
    # 过滤配置
    keywords = Column(JSON, comment="关键词过滤")
    exclude_keywords = Column(JSON, comment="排除关键词")
    category = Column(String(50), comment="分类")
    
    # 状态配置
    is_active = Column(Boolean, default=True, comment="是否启用")
    priority = Column(Integer, default=1, comment="优先级")
    crawl_interval = Column(Integer, default=3600, comment="爬取间隔(秒)")
    
    # 统计信息
    total_crawled = Column(Integer, default=0, comment="总爬取数量")
    success_rate = Column(Integer, default=0, comment="成功率")
    last_crawl_time = Column(DateTime(timezone=True), comment="最后爬取时间")
    last_success_time = Column(DateTime(timezone=True), comment="最后成功时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    contents = relationship("Content", back_populates="news_source")

    def __repr__(self):
        return f"<NewsSource(id={self.id}, name='{self.name}')>"
