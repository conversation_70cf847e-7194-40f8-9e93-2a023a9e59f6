"""
内容模型
"""
from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base import Base


class Content(Base):
    """内容表"""
    __tablename__ = "contents"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(500), nullable=False, comment="标题")
    original_title = Column(String(500), comment="原始标题")
    content = Column(Text, comment="内容")
    original_content = Column(Text, comment="原始内容")
    summary = Column(Text, comment="摘要")
    
    # 来源信息
    source_url = Column(String(1000), comment="原始URL")
    source_name = Column(String(100), comment="来源名称")
    author = Column(String(100), comment="作者")
    
    # 分类和标签
    category = Column(String(50), comment="分类")
    tags = Column(JSON, comment="标签列表")
    keywords = Column(JSON, comment="关键词")
    
    # 质量评分
    quality_score = Column(Integer, comment="质量评分")
    originality_score = Column(Integer, comment="原创性评分")
    readability_score = Column(Integer, comment="可读性评分")
    
    # 状态信息
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    is_published = Column(Boolean, default=False, comment="是否已发布")
    is_approved = Column(Boolean, default=False, comment="是否已审核")
    
    # 外键关联
    news_source_id = Column(Integer, ForeignKey("news_sources.id"), comment="资讯源ID")
    
    # 时间信息
    published_at = Column(DateTime(timezone=True), comment="原始发布时间")
    crawled_at = Column(DateTime(timezone=True), comment="爬取时间")
    processed_at = Column(DateTime(timezone=True), comment="处理时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 关联关系
    news_source = relationship("NewsSource", back_populates="contents")
    
    publications = relationship("Publication", back_populates="content")

    def __repr__(self):
        return f"<Content(id={self.id}, title='{self.title[:50]}...')>"
