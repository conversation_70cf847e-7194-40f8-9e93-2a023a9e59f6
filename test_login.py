#!/usr/bin/env python3
"""
测试登录API
"""

import requests
import json

def test_login_api():
    """测试登录API"""
    print("测试后端登录API")
    print("=" * 30)
    
    # 测试数据
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        # 发送登录请求
        response = requests.post(
            "http://localhost:8000/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("\n✅ 登录成功!")
            print(f"Token: {data.get('access_token')}")
            print(f"用户ID: {data.get('user_id')}")
            print(f"用户名: {data.get('username')}")
            
            # 测试获取用户信息
            test_user_info(data.get('access_token'))
        else:
            print(f"\n❌ 登录失败: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务，请确保后端正在运行")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_user_info(token):
    """测试获取用户信息API"""
    print("\n测试获取用户信息API")
    print("-" * 30)
    
    try:
        response = requests.get(
            "http://localhost:8000/api/v1/auth/me",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 获取用户信息成功!")
        else:
            print("❌ 获取用户信息失败")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def test_health():
    """测试健康检查"""
    print("测试健康检查API")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print("❌ 后端服务异常")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务")
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

if __name__ == "__main__":
    test_health()
    print()
    test_login_api()
