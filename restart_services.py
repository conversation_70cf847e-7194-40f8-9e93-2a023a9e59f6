#!/usr/bin/env python3
"""
重启服务脚本
"""

import subprocess
import sys
import time
import os

def kill_existing_processes():
    """杀死现有进程"""
    print("🔄 停止现有服务...")
    
    try:
        # 杀死占用8000端口的进程
        subprocess.run(
            ["taskkill", "/F", "/IM", "python.exe"],
            capture_output=True,
            shell=True
        )
        print("   ✅ 停止Python进程")
    except:
        print("   ⚠️ 无法停止Python进程")
    
    try:
        # 杀死占用5173端口的进程
        subprocess.run(
            ["taskkill", "/F", "/IM", "node.exe"],
            capture_output=True,
            shell=True
        )
        print("   ✅ 停止Node.js进程")
    except:
        print("   ⚠️ 无法停止Node.js进程")
    
    time.sleep(2)

def start_backend():
    """启动后端"""
    print("\n🚀 启动后端服务...")
    
    if not os.path.exists("backend"):
        print("   ❌ 后端目录不存在")
        return False
    
    try:
        # 启动后端
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--host", "127.0.0.1",  # 明确指定127.0.0.1
            "--port", "8000",
            "--reload"
        ]
        
        print(f"   执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            cwd="backend",
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        # 等待启动
        time.sleep(3)
        
        if process.poll() is None:
            print("   ✅ 后端启动成功")
            print("   📍 后端地址: http://127.0.0.1:8000")
            return True
        else:
            print("   ❌ 后端启动失败")
            output = process.stdout.read()
            if output:
                print(f"   错误: {output}")
            return False
            
    except Exception as e:
        print(f"   ❌ 启动后端时出错: {str(e)}")
        return False

def start_frontend():
    """启动前端"""
    print("\n🚀 启动前端服务...")
    
    if not os.path.exists("frontend"):
        print("   ❌ 前端目录不存在")
        return False
    
    try:
        # 检查是否有node_modules
        if not os.path.exists("frontend/node_modules"):
            print("   ⚠️ 未发现node_modules，正在安装依赖...")
            subprocess.run(["npm", "install"], cwd="frontend", check=True)
        
        # 启动前端开发服务器
        cmd = ["npm", "run", "dev", "--", "--host", "127.0.0.1"]
        
        print(f"   执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            cwd="frontend",
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )
        
        # 等待启动
        time.sleep(5)
        
        if process.poll() is None:
            print("   ✅ 前端启动成功")
            print("   📍 前端地址: http://127.0.0.1:5173")
            return True
        else:
            print("   ❌ 前端启动失败")
            output = process.stdout.read()
            if output:
                print(f"   错误: {output}")
            return False
            
    except Exception as e:
        print(f"   ❌ 启动前端时出错: {str(e)}")
        return False

def test_services():
    """测试服务"""
    print("\n🔍 测试服务...")
    
    import requests
    
    # 测试后端
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ 后端服务正常")
        else:
            print(f"   ⚠️ 后端服务状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 后端服务测试失败: {str(e)}")
    
    # 测试前端
    try:
        response = requests.get("http://127.0.0.1:5173", timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端服务正常")
        else:
            print(f"   ⚠️ 前端服务状态码: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 前端服务测试失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 服务重启工具")
    print("=" * 50)
    
    # 停止现有服务
    kill_existing_processes()
    
    # 启动后端
    backend_ok = start_backend()
    
    # 启动前端
    frontend_ok = start_frontend()
    
    # 测试服务
    if backend_ok or frontend_ok:
        test_services()
    
    print("\n" + "=" * 50)
    if backend_ok and frontend_ok:
        print("🎉 服务启动成功!")
        print("📍 前端地址: http://127.0.0.1:5173")
        print("📍 后端地址: http://127.0.0.1:8000")
        print("📍 API文档: http://127.0.0.1:8000/api/docs")
    elif backend_ok:
        print("⚠️ 仅后端启动成功")
        print("📍 后端地址: http://127.0.0.1:8000")
    elif frontend_ok:
        print("⚠️ 仅前端启动成功")
        print("📍 前端地址: http://127.0.0.1:5173")
    else:
        print("❌ 服务启动失败")
        print("请检查错误信息并手动启动")

if __name__ == "__main__":
    main()
