#!/usr/bin/env python3
"""
立即构建测试
"""

import subprocess
import re

def build_now():
    """立即构建"""
    print("🚀 开始构建...")
    
    try:
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="frontend",
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=60
        )
        
        print("构建输出:")
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("构建错误/警告:")
            print(result.stderr)
            
            # 分析图标错误
            if "is not exported by" in result.stderr and "icons-vue" in result.stderr:
                icon_match = re.search(r'"([^"]+)" is not exported by.*icons-vue', result.stderr)
                file_match = re.search(r'imported by "([^"]+)"', result.stderr)
                
                if icon_match and file_match:
                    missing_icon = icon_match.group(1)
                    problem_file = file_match.group(1)
                    
                    print(f"\n❌ 图标问题:")
                    print(f"  缺失图标: {missing_icon}")
                    print(f"  问题文件: {problem_file}")
                    
                    # 建议修复
                    suggestions = {
                        'Robot': 'Cpu',
                        'Play': 'Cpu',
                        'Pause': 'Monitor',
                        'Stop': 'Monitor',
                        'Record': 'Document'
                    }
                    
                    if missing_icon in suggestions:
                        print(f"  建议替换: {missing_icon} → {suggestions[missing_icon]}")
                    
                    return False, missing_icon, problem_file
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            return True, None, None
        else:
            print(f"❌ 构建失败，退出码: {result.returncode}")
            return False, None, None
            
    except Exception as e:
        print(f"❌ 构建出错: {str(e)}")
        return False, None, None

if __name__ == "__main__":
    success, missing_icon, problem_file = build_now()
    
    if success:
        print("\n🎉 构建成功!")
        print("现在可以:")
        print("1. 运行 'python start_full_system.py' 启动系统")
        print("2. 或运行 'cd frontend && npm run preview' 预览")
    else:
        if missing_icon and problem_file:
            print(f"\n需要修复: {problem_file} 中的 {missing_icon} 图标")
        print("请修复后重新构建")
