# Redis配置文件
# 用于AI资讯智能体系统

# 基本配置
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 300

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice
logfile "redis.log"

# 安全配置
# 启用ACL（访问控制列表）
aclfile users.acl

# 或者使用传统的requirepass（二选一）
# requirepass 000001

# 数据库数量
databases 16

# 客户端连接配置
maxclients 10000

# 网络配置
tcp-backlog 511

# 其他配置
always-show-logo yes
