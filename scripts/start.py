#!/usr/bin/env python3
"""
应用启动脚本
"""
import os
import sys
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import redis
        import celery
        print("✅ 后端依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False
    return True

def check_services():
    """检查服务是否运行"""
    services = {
        "PostgreSQL": ("localhost", 5432),
        "Redis": ("localhost", 6379),
        "MongoDB": ("localhost", 27017)
    }
    
    for service, (host, port) in services.items():
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"✅ {service} 运行正常")
            else:
                print(f"❌ {service} 未运行 ({host}:{port})")
                return False
        except Exception as e:
            print(f"❌ 检查 {service} 时出错: {e}")
            return False
    
    return True

def setup_database():
    """初始化数据库"""
    print("🔧 初始化数据库...")
    try:
        from backend.app.db.base import engine, Base
        from backend.app.models import *
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建成功")
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False
    
    return True

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端服务...")
    os.chdir(project_root / "backend")
    
    # 启动FastAPI应用
    cmd = [
        sys.executable, "-m", "uvicorn",
        "app.main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ]
    
    return subprocess.Popen(cmd)

def start_celery():
    """启动Celery worker"""
    print("🔄 启动Celery worker...")
    os.chdir(project_root / "backend")
    
    cmd = [
        sys.executable, "-m", "celery",
        "-A", "app.core.celery_app",
        "worker",
        "--loglevel=info"
    ]
    
    return subprocess.Popen(cmd)

def start_frontend():
    """启动前端开发服务器"""
    print("🎨 启动前端服务...")
    os.chdir(project_root / "frontend")
    
    # 检查是否安装了依赖
    if not (project_root / "frontend" / "node_modules").exists():
        print("📦 安装前端依赖...")
        subprocess.run(["npm", "install"], check=True)
    
    cmd = ["npm", "run", "dev"]
    return subprocess.Popen(cmd)

def main():
    """主函数"""
    print("🎯 AI资讯智能体启动脚本")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查服务
    if not check_services():
        print("请确保 PostgreSQL、Redis 和 MongoDB 服务正在运行")
        return
    
    # 初始化数据库
    if not setup_database():
        return
    
    processes = []
    
    try:
        # 启动后端
        backend_process = start_backend()
        processes.append(backend_process)
        time.sleep(3)  # 等待后端启动
        
        # 启动Celery
        celery_process = start_celery()
        processes.append(celery_process)
        time.sleep(2)  # 等待Celery启动
        
        # 启动前端
        frontend_process = start_frontend()
        processes.append(frontend_process)
        
        print("\n🎉 所有服务启动完成!")
        print("📱 前端地址: http://localhost:3000")
        print("🔧 后端API: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/api/docs")
        print("\n按 Ctrl+C 停止所有服务")
        
        # 等待用户中断
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        for process in processes:
            process.terminate()
        
        # 等待进程结束
        for process in processes:
            process.wait()
        
        print("✅ 所有服务已停止")

if __name__ == "__main__":
    main()
