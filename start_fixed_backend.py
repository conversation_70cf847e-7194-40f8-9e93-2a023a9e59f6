#!/usr/bin/env python3
"""
启动修复后的后端
"""

import subprocess
import sys
import os
import time

def kill_existing_backend():
    """停止现有后端"""
    print("🔄 停止现有后端...")
    
    try:
        # 停止Python进程
        subprocess.run(
            ["taskkill", "/F", "/IM", "python.exe"],
            capture_output=True,
            shell=True
        )
        print("   ✅ 已停止现有Python进程")
        time.sleep(2)
    except:
        print("   ⚠️ 无法停止现有进程")

def start_fixed_backend():
    """启动修复后的后端"""
    print("🚀 启动修复后的后端...")
    
    if not os.path.exists("backend/main_minimal.py"):
        print("❌ 修复后的后端文件不存在")
        print("请先运行: python fix_backend_routes.py")
        return False
    
    try:
        # 使用uvicorn启动
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main_minimal:app",
            "--host", "127.0.0.1",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("在backend目录中启动...")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd="backend"
        )
        
        print("✅ 后端启动中...")
        print("📍 访问地址: http://127.0.0.1:8000")
        print("📍 API文档: http://127.0.0.1:8000/api/docs")
        print("📍 健康检查: http://127.0.0.1:8000/health")
        
        # 等待启动
        print("\n等待服务启动...")
        time.sleep(3)
        
        # 测试服务
        import requests
        try:
            response = requests.get("http://127.0.0.1:8000/health", timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务启动成功!")
                print("🎉 现在可以在浏览器访问: http://127.0.0.1:8000")
                return True
            else:
                print(f"⚠️ 服务响应异常: {response.status_code}")
        except Exception as e:
            print(f"⚠️ 服务测试失败: {str(e)}")
            print("服务可能仍在启动中...")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return False

def test_all_apis():
    """测试所有API"""
    print("\n🔍 测试所有API...")
    
    import requests
    import json
    
    base_url = "http://127.0.0.1:8000"
    
    apis = [
        ("GET", "/", "根路径"),
        ("GET", "/health", "健康检查"),
        ("GET", "/api/docs", "API文档"),
        ("POST", "/api/v1/auth/login", "登录API", {"username": "admin", "password": "admin123"}),
        ("GET", "/api/v1/users/", "用户列表")
    ]
    
    for method, path, name, *data in apis:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{path}", timeout=5)
            else:
                response = requests.post(f"{base_url}{path}", json=data[0] if data else {}, timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ {name} ({path})")
            else:
                print(f"   ⚠️ {name} ({path}) - 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ {name} ({path}) - 错误: {str(e)}")

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 启动修复后的后端")
    print("=" * 50)
    
    # 停止现有后端
    kill_existing_backend()
    
    # 启动修复后的后端
    if start_fixed_backend():
        # 测试API
        test_all_apis()
        
        print("\n" + "=" * 50)
        print("🎉 后端启动完成!")
        print("📍 前端访问: http://127.0.0.1:8000")
        print("📍 API文档: http://127.0.0.1:8000/api/docs")
        print("📍 登录账号: admin / admin123")
        print("\n按 Ctrl+C 可以停止服务")
        
        # 保持运行
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止服务...")
            kill_existing_backend()
            print("✅ 服务已停止")
    else:
        print("❌ 后端启动失败")

if __name__ == "__main__":
    main()
