#!/usr/bin/env python3
"""
测试前端构建
"""

import subprocess
import os
import sys

def test_build():
    """测试构建"""
    print("测试前端构建...")
    
    # 切换到前端目录
    frontend_dir = "frontend"
    if not os.path.exists(frontend_dir):
        print("❌ 前端目录不存在")
        return False
    
    try:
        # 运行构建命令
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd=frontend_dir,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        print("构建输出:")
        print(result.stdout)
        
        if result.stderr:
            print("构建错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            
            # 检查构建文件
            dist_dir = os.path.join(frontend_dir, "dist")
            if os.path.exists(dist_dir):
                print(f"✅ 构建文件已生成: {dist_dir}")
                
                # 列出构建文件
                for root, dirs, files in os.walk(dist_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        rel_path = os.path.relpath(file_path, dist_dir)
                        print(f"  📄 {rel_path}")
                
                return True
            else:
                print("❌ 构建文件目录不存在")
                return False
        else:
            print(f"❌ 构建失败，退出码: {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_build()
    if not success:
        sys.exit(1)
