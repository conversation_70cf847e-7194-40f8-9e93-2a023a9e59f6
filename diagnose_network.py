#!/usr/bin/env python3
"""
网络诊断工具
"""

import socket
import requests
import subprocess
import time

def check_port_binding(host, port):
    """检查端口绑定情况"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(2)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        return False

def find_listening_ports():
    """查找监听的端口"""
    print("🔍 查找监听的端口...")
    
    try:
        # Windows命令
        result = subprocess.run(
            ["netstat", "-an"],
            capture_output=True,
            text=True,
            shell=True
        )
        
        lines = result.stdout.split('\n')
        listening_ports = []
        
        for line in lines:
            if 'LISTENING' in line and ('8000' in line or '5173' in line):
                listening_ports.append(line.strip())
        
        if listening_ports:
            print("   发现监听端口:")
            for port in listening_ports:
                print(f"   {port}")
        else:
            print("   ❌ 未发现8000或5173端口监听")
            
        return listening_ports
        
    except Exception as e:
        print(f"   ❌ 查找端口失败: {str(e)}")
        return []

def test_different_hosts():
    """测试不同的主机地址"""
    print("\n🔍 测试不同的主机地址...")
    
    hosts = ['localhost', '127.0.0.1', '0.0.0.0']
    ports = [8000, 5173]
    
    working_addresses = []
    
    for port in ports:
        print(f"\n   测试端口 {port}:")
        for host in hosts:
            if check_port_binding(host, port):
                print(f"   ✅ {host}:{port} - 可连接")
                working_addresses.append(f"{host}:{port}")
                
                # 尝试HTTP请求
                try:
                    if port == 8000:
                        response = requests.get(f"http://{host}:{port}/", timeout=3)
                        if response.status_code == 200:
                            print(f"      ✅ HTTP请求成功")
                        else:
                            print(f"      ⚠️ HTTP请求状态码: {response.status_code}")
                    elif port == 5173:
                        response = requests.get(f"http://{host}:{port}/", timeout=3)
                        if response.status_code == 200:
                            print(f"      ✅ HTTP请求成功")
                        else:
                            print(f"      ⚠️ HTTP请求状态码: {response.status_code}")
                except Exception as e:
                    print(f"      ❌ HTTP请求失败: {str(e)}")
            else:
                print(f"   ❌ {host}:{port} - 无法连接")
    
    return working_addresses

def test_api_endpoints(base_url):
    """测试API端点"""
    print(f"\n🔍 测试API端点 ({base_url})...")
    
    endpoints = [
        "/",
        "/health", 
        "/api/v1/auth/login"
    ]
    
    for endpoint in endpoints:
        try:
            if endpoint == "/api/v1/auth/login":
                # POST请求
                response = requests.post(
                    f"{base_url}{endpoint}",
                    json={"username": "admin", "password": "admin123"},
                    timeout=5
                )
            else:
                # GET请求
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            
            print(f"   {endpoint}: {response.status_code}")
            if response.status_code == 200:
                print(f"      ✅ 成功")
            else:
                print(f"      ⚠️ 状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   {endpoint}: ❌ 错误 - {str(e)}")

def check_browser_access():
    """检查浏览器访问"""
    print("\n🔍 检查浏览器可能的访问地址...")
    
    possible_urls = [
        "http://localhost:8000",
        "http://127.0.0.1:8000", 
        "http://localhost:5173",
        "http://127.0.0.1:5173"
    ]
    
    for url in possible_urls:
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                print(f"   ✅ {url} - 可访问")
                
                # 检查是否是前端页面
                if "AI资讯智能体" in response.text or "login" in response.text.lower():
                    print(f"      🎯 这可能是你的前端页面!")
                    return url
            else:
                print(f"   ⚠️ {url} - 状态码: {response.status_code}")
        except Exception as e:
            print(f"   ❌ {url} - 无法访问")
    
    return None

def main():
    """主函数"""
    print("=" * 60)
    print("AI资讯智能体系统 - 网络诊断工具")
    print("=" * 60)
    
    # 查找监听端口
    listening_ports = find_listening_ports()
    
    # 测试不同主机地址
    working_addresses = test_different_hosts()
    
    # 检查浏览器访问
    frontend_url = check_browser_access()
    
    print("\n" + "=" * 60)
    print("诊断结果:")
    
    if working_addresses:
        print("✅ 发现可用的服务地址:")
        for addr in working_addresses:
            print(f"   📍 {addr}")
            
            if "8000" in addr:
                base_url = f"http://{addr.replace(':8000', ':8000')}"
                test_api_endpoints(base_url)
    
    if frontend_url:
        print(f"\n🎯 推荐的前端访问地址: {frontend_url}")
        print("请在浏览器中访问这个地址!")
    else:
        print("\n❌ 未找到可访问的前端地址")
        print("建议:")
        print("1. 重新启动后端: python start_backend_simple.py")
        print("2. 重新启动前端: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
