#!/usr/bin/env python3
"""
快速构建测试
"""

import subprocess
import os

def quick_build():
    """快速构建测试"""
    print("🚀 开始构建前端...")
    
    try:
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="frontend",
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=60
        )
        
        print("构建输出:")
        print(result.stdout)
        
        if result.stderr:
            print("构建信息/警告:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            
            # 检查构建结果
            dist_path = "frontend/dist"
            if os.path.exists(dist_path):
                print(f"✅ 构建文件已生成: {dist_path}")
                
                # 统计文件
                file_count = 0
                for root, dirs, files in os.walk(dist_path):
                    file_count += len(files)
                
                print(f"📊 构建文件数量: {file_count}")
                return True
            else:
                print("❌ 构建目录不存在")
                return False
        else:
            print(f"❌ 构建失败，退出码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 构建出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = quick_build()
    if success:
        print("\n🎉 前端构建完成!")
        print("现在可以:")
        print("1. 运行 'python start_full_system.py' 启动完整系统")
        print("2. 或运行 'cd frontend && npm run preview' 预览构建结果")
    else:
        print("\n❌ 构建失败，请检查错误信息")
