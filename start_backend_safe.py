#!/usr/bin/env python3
"""
安全启动后端
"""

import subprocess
import sys
import os
import time

def start_backend_directly():
    """直接启动后端，不停止现有进程"""
    print("🚀 直接启动修复后的后端...")
    
    if not os.path.exists("backend/main_minimal.py"):
        print("❌ 修复后的后端文件不存在")
        print("请先运行: python fix_backend_routes.py")
        return False
    
    try:
        # 使用uvicorn启动，指定不同端口避免冲突
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main_minimal:app",
            "--host", "127.0.0.1",
            "--port", "8001",  # 使用8001端口避免冲突
            "--reload",
            "--log-level", "info"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("在backend目录中启动...")
        print("使用端口8001避免与现有服务冲突")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd="backend"
        )
        
        print("✅ 后端启动中...")
        print("📍 访问地址: http://127.0.0.1:8001")
        print("📍 API文档: http://127.0.0.1:8001/api/docs")
        print("📍 健康检查: http://127.0.0.1:8001/health")
        
        # 等待启动
        print("\n等待服务启动...")
        time.sleep(5)
        
        # 测试服务
        import requests
        try:
            response = requests.get("http://127.0.0.1:8001/health", timeout=10)
            if response.status_code == 200:
                print("✅ 后端服务启动成功!")
                print("🎉 现在可以在浏览器访问: http://127.0.0.1:8001")
                return True
            else:
                print(f"⚠️ 服务响应异常: {response.status_code}")
        except Exception as e:
            print(f"⚠️ 服务测试失败: {str(e)}")
            print("服务可能仍在启动中...")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return False

def test_apis():
    """测试API"""
    print("\n🔍 测试API...")
    
    import requests
    
    base_url = "http://127.0.0.1:8001"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ 健康检查正常")
        else:
            print(f"   ⚠️ 健康检查异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 健康检查失败: {str(e)}")
    
    # 测试登录API
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data, timeout=5)
        if response.status_code == 200:
            print("   ✅ 登录API正常")
        else:
            print(f"   ⚠️ 登录API异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 登录API失败: {str(e)}")
    
    # 测试API文档
    try:
        response = requests.get(f"{base_url}/api/docs", timeout=5)
        if response.status_code == 200:
            print("   ✅ API文档可访问")
        else:
            print(f"   ⚠️ API文档异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API文档失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 安全启动后端")
    print("=" * 50)
    
    # 直接启动后端
    if start_backend_directly():
        # 测试API
        test_apis()
        
        print("\n" + "=" * 50)
        print("🎉 后端启动完成!")
        print("📍 前端访问: http://127.0.0.1:8001")
        print("📍 API文档: http://127.0.0.1:8001/api/docs")
        print("📍 登录账号: admin / admin123")
        print("\n服务在后台运行，可以关闭此窗口")
    else:
        print("❌ 后端启动失败")

if __name__ == "__main__":
    main()
