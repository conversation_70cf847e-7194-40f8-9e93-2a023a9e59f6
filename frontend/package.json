{"name": "ai-news-agent-frontend", "version": "1.0.0", "description": "AI资讯智能体前端应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-with-check": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.3.8", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.2", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.0", "socket.io-client": "^4.7.4", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "dayjs": "^1.11.10", "date-fns": "^3.0.6", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/node": "^20.9.0", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^4.4.1", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "prettier": "^3.1.0", "typescript": "~5.2.2", "vite": "^4.5.0", "vue-tsc": "^1.8.22"}}