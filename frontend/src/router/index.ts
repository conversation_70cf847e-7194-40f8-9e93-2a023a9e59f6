import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表板',
      requiresAuth: true
    }
  },
  {
    path: '/news-sources',
    name: 'NewsSources',
    component: () => import('@/views/news/NewsSources.vue'),
    meta: {
      title: '资讯源管理',
      requiresAuth: true
    }
  },
  {
    path: '/contents',
    name: 'Contents',
    component: () => import('@/views/content/Contents.vue'),
    meta: {
      title: '内容管理',
      requiresAuth: true
    }
  },
  {
    path: '/content/edit/:id?',
    name: 'ContentEdit',
    component: () => import('@/views/content/ContentEdit.vue'),
    meta: {
      title: '内容编辑',
      requiresAuth: true
    }
  },
  {
    path: '/publications',
    name: 'Publications',
    component: () => import('@/views/publish/Publications.vue'),
    meta: {
      title: '发布管理',
      requiresAuth: true
    }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: () => import('@/views/task/Tasks.vue'),
    meta: {
      title: '任务管理',
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/system/Settings.vue'),
    meta: {
      title: '系统设置',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - AI资讯智能体`
  }
  
  // 检查认证状态
  const token = localStorage.getItem('token')
  if (to.meta?.requiresAuth && !token) {
    next('/login')
  } else {
    next()
  }
})

export default router
