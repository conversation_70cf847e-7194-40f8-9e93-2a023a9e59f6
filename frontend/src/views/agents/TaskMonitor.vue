<template>
  <Layout>
    <div class="task-monitor">
    <el-card class="page-header">
      <h2>任务监控</h2>
      <p>实时监控AI智能体任务执行状态</p>
    </el-card>

    <!-- 任务统计 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon running">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.running }}</div>
              <div class="stat-label">运行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.success }}</div>
              <div class="stat-label">成功</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon failed">
              <el-icon><CircleClose /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-icon total">
              <el-icon><DataAnalysis /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ taskStats.total }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务列表 -->
    <el-card class="tasks-card">
      <template #header>
        <div class="card-header">
          <span>任务列表</span>
          <div class="header-actions">
            <el-select v-model="filters.status" placeholder="状态筛选" clearable @change="loadTasks">
              <el-option label="等待中" value="PENDING" />
              <el-option label="运行中" value="RUNNING" />
              <el-option label="成功" value="SUCCESS" />
              <el-option label="失败" value="FAILED" />
              <el-option label="已取消" value="CANCELLED" />
            </el-select>
            <el-select v-model="filters.task_type" placeholder="类型筛选" clearable @change="loadTasks">
              <el-option label="新闻爬取" value="crawl_news" />
              <el-option label="内容改写" value="rewrite_content" />
              <el-option label="质量检查" value="quality_check" />
              <el-option label="内容发布" value="publish_content" />
            </el-select>
            <el-button type="primary" @click="loadTasks">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="danger" @click="cleanupOldTasks">
              <el-icon><Delete /></el-icon>
              清理旧任务
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="tasks" v-loading="loading">
        <el-table-column prop="task_id" label="任务ID" width="200">
          <template #default="{ row }">
            <el-text class="task-id" @click="viewTaskDetails(row)">
              {{ row.task_id.substring(0, 8) }}...
            </el-text>
          </template>
        </el-table-column>
        <el-table-column prop="task_type" label="任务类型" width="150">
          <template #default="{ row }">
            <el-tag size="small">{{ getTaskTypeText(row.task_type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="进度" width="200">
          <template #default="{ row }">
            <div class="progress-container">
              <el-progress 
                :percentage="row.progress" 
                :status="getProgressStatus(row.status)"
                :stroke-width="8"
              />
              <div class="progress-text">{{ row.current_step || '等待中' }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button 
              v-if="canCancel(row.status)"
              type="warning" 
              size="small" 
              @click="cancelTask(row)"
            >
              取消
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              @click="viewTaskDetails(row)"
            >
              详情
            </el-button>
            <el-button 
              v-if="canDelete(row.status)"
              type="danger" 
              size="small" 
              @click="deleteTask(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.page_size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadTasks"
          @current-change="loadTasks"
        />
      </div>
    </el-card>

    <!-- 任务详情对话框 -->
    <el-dialog 
      v-model="showTaskDialog" 
      title="任务详情" 
      width="800px"
    >
      <div v-if="selectedTask" class="task-details">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">
            {{ selectedTask.task_id }}
          </el-descriptions-item>
          <el-descriptions-item label="任务类型">
            {{ getTaskTypeText(selectedTask.task_type) }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="进度">
            {{ selectedTask.progress }}%
          </el-descriptions-item>
          <el-descriptions-item label="当前步骤" :span="2">
            {{ selectedTask.current_step || '无' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(selectedTask.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatTime(selectedTask.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="selectedTask.error_message" label="错误信息" :span="2">
            <el-text type="danger">{{ selectedTask.error_message }}</el-text>
          </el-descriptions-item>
        </el-descriptions>

        <!-- Celery任务信息 -->
        <div v-if="celeryTaskInfo" class="celery-info">
          <h4>Celery任务信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="Celery状态">
              {{ celeryTaskInfo.status }}
            </el-descriptions-item>
            <el-descriptions-item label="结果" :span="2">
              <pre v-if="celeryTaskInfo.result">{{ JSON.stringify(celeryTaskInfo.result, null, 2) }}</pre>
              <span v-else>无</span>
            </el-descriptions-item>
            <el-descriptions-item v-if="celeryTaskInfo.traceback" label="错误堆栈" :span="2">
              <pre class="error-traceback">{{ celeryTaskInfo.traceback }}</pre>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <template #footer>
        <el-button @click="showTaskDialog = false">关闭</el-button>
        <el-button 
          v-if="selectedTask && canCancel(selectedTask.status)"
          type="warning" 
          @click="cancelTask(selectedTask)"
        >
          取消任务
        </el-button>
      </template>
    </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Loading, CircleCheck, CircleClose, DataAnalysis,
  Refresh, Delete
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { taskApi } from '@/api/agents'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

// 响应式数据
const loading = ref(false)
const showTaskDialog = ref(false)
const selectedTask = ref(null)
const celeryTaskInfo = ref(null)

const tasks = ref([])
const taskStats = reactive({
  running: 0,
  success: 0,
  failed: 0,
  total: 0
})

const filters = reactive({
  status: '',
  task_type: ''
})

const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 定时刷新
let refreshTimer: NodeJS.Timeout | null = null

// 方法
const loadTasks = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.page_size,
      ...filters
    }
    
    const response = await taskApi.getTasks(params)
    if (response.success) {
      tasks.value = response.data.tasks
      pagination.total = response.data.total
    }
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadTaskStats = async () => {
  try {
    const response = await taskApi.getTaskStats()
    if (response.success) {
      const stats = response.data
      taskStats.total = stats.total_tasks
      
      // 统计各状态数量
      const statusDist = stats.status_distribution
      taskStats.running = statusDist.find(s => s.status === 'RUNNING')?.count || 0
      taskStats.success = statusDist.find(s => s.status === 'SUCCESS')?.count || 0
      taskStats.failed = statusDist.find(s => s.status === 'FAILED')?.count || 0
    }
  } catch (error) {
    console.error('加载任务统计失败:', error)
  }
}

const viewTaskDetails = async (task: any) => {
  selectedTask.value = task
  showTaskDialog.value = true
  
  // 加载Celery任务信息
  try {
    const response = await taskApi.getCeleryTaskInfo(task.task_id)
    if (response.success) {
      celeryTaskInfo.value = response.data
    }
  } catch (error) {
    console.error('加载Celery任务信息失败:', error)
  }
}

const cancelTask = async (task: any) => {
  try {
    await ElMessageBox.confirm('确定要取消这个任务吗？', '确认取消', {
      type: 'warning'
    })
    
    const response = await taskApi.cancelTask(task.task_id)
    if (response.success) {
      ElMessage.success('任务已取消')
      loadTasks()
      loadTaskStats()
      if (showTaskDialog.value) {
        showTaskDialog.value = false
      }
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消任务失败')
    }
  }
}

const deleteTask = async (task: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个任务记录吗？', '确认删除', {
      type: 'warning'
    })
    
    const response = await taskApi.deleteTask(task.task_id)
    if (response.success) {
      ElMessage.success('任务已删除')
      loadTasks()
      loadTaskStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除任务失败')
    }
  }
}

const cleanupOldTasks = async () => {
  try {
    await ElMessageBox.confirm('确定要清理30天前的旧任务记录吗？', '确认清理', {
      type: 'warning'
    })
    
    const response = await taskApi.cleanupOldTasks(30)
    if (response.success) {
      ElMessage.success(response.message)
      loadTasks()
      loadTaskStats()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清理任务失败')
    }
  }
}

const canCancel = (status: string) => {
  return ['PENDING', 'RUNNING'].includes(status)
}

const canDelete = (status: string) => {
  return ['SUCCESS', 'FAILED', 'CANCELLED'].includes(status)
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'RUNNING': return 'warning'
    case 'CANCELLED': return 'info'
    default: return ''
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return '等待中'
    case 'RUNNING': return '运行中'
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    case 'CANCELLED': return '已取消'
    default: return status
  }
}

const getProgressStatus = (status: string) => {
  switch (status) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'exception'
    default: return ''
  }
}

const getTaskTypeText = (taskType: string) => {
  const typeMap = {
    'crawl_news': '新闻爬取',
    'rewrite_content': '内容改写',
    'quality_check': '质量检查',
    'publish_content': '内容发布',
    'batch_process': '批量处理'
  }
  return typeMap[taskType] || taskType
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

const startAutoRefresh = () => {
  refreshTimer = setInterval(() => {
    loadTasks()
    loadTaskStats()
  }, 5000) // 每5秒刷新一次
}

const stopAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 生命周期
onMounted(() => {
  loadTasks()
  loadTaskStats()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.task-monitor {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.running { background-color: #e6a23c; }
.stat-icon.success { background-color: #67c23a; }
.stat-icon.failed { background-color: #f56c6c; }
.stat-icon.total { background-color: #409eff; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 5px;
}

.tasks-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.task-id {
  cursor: pointer;
  color: #409eff;
}

.task-id:hover {
  text-decoration: underline;
}

.progress-container {
  width: 100%;
}

.progress-text {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.task-details {
  margin-bottom: 20px;
}

.celery-info {
  margin-top: 20px;
}

.celery-info h4 {
  margin-bottom: 10px;
  color: #303133;
}

.error-traceback {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
