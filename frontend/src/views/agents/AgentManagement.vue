<template>
  <Layout>
    <div class="agent-management">
    <el-card class="page-header">
      <h2>AI智能体管理</h2>
      <p>管理和监控AI资讯处理智能体</p>
    </el-card>

    <!-- 智能体状态概览 -->
    <el-row :gutter="20" class="status-cards">
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon success">
              <el-icon><Check /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ agentStats.healthy }}</div>
              <div class="status-label">健康智能体</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon warning">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ agentStats.unhealthy }}</div>
              <div class="status-label">异常智能体</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon info">
              <el-icon><Operation /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ agentStats.total }}</div>
              <div class="status-label">总智能体数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="status-card">
          <div class="status-item">
            <div class="status-icon primary">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="status-content">
              <div class="status-value">{{ workflowStats.total }}</div>
              <div class="status-label">工作流程</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 智能体列表 -->
    <el-card class="agents-card">
      <template #header>
        <div class="card-header">
          <span>智能体列表</span>
          <el-button type="primary" @click="refreshAgents">
            <el-icon><Refresh /></el-icon>
            刷新状态
          </el-button>
        </div>
      </template>

      <el-table :data="agents" v-loading="loading">
        <el-table-column prop="name" label="智能体名称" width="200">
          <template #default="{ row }">
            <div class="agent-name">
              <el-icon class="agent-icon"><Cpu /></el-icon>
              {{ row.name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" min-width="300" />
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="必需字段" width="200">
          <template #default="{ row }">
            <el-tag 
              v-for="field in row.required_fields" 
              :key="field" 
              size="small" 
              class="field-tag"
            >
              {{ field }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click="executeAgent(row)"
            >
              执行
            </el-button>
            <el-button 
              type="info" 
              size="small" 
              @click="viewAgentDetails(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 工作流程管理 -->
    <el-card class="workflows-card">
      <template #header>
        <div class="card-header">
          <span>工作流程</span>
          <el-button type="success" @click="showWorkflowDialog = true">
            <el-icon><Plus /></el-icon>
            执行工作流程
          </el-button>
        </div>
      </template>

      <el-table :data="workflows" v-loading="workflowLoading">
        <el-table-column prop="name" label="流程名称" width="200" />
        <el-table-column prop="description" label="描述" min-width="300" />
        <el-table-column label="步骤" width="300">
          <template #default="{ row }">
            <div class="workflow-steps">
              <el-tag 
                v-for="(step, index) in row.steps" 
                :key="step"
                size="small"
                class="step-tag"
              >
                {{ index + 1 }}. {{ step }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <el-button 
              type="success" 
              size="small" 
              @click="executeWorkflow(row)"
            >
              执行
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 执行智能体对话框 -->
    <el-dialog 
      v-model="showAgentDialog" 
      title="执行智能体" 
      width="600px"
    >
      <el-form :model="agentForm" label-width="120px">
        <el-form-item label="智能体">
          <el-input v-model="agentForm.agent_name" disabled />
        </el-form-item>
        <el-form-item label="任务数据">
          <el-input 
            v-model="agentForm.task_data_json" 
            type="textarea" 
            :rows="8"
            placeholder="请输入JSON格式的任务数据"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAgentDialog = false">取消</el-button>
        <el-button type="primary" @click="submitAgentExecution" :loading="executing">
          执行
        </el-button>
      </template>
    </el-dialog>

    <!-- 执行工作流程对话框 -->
    <el-dialog 
      v-model="showWorkflowDialog" 
      title="执行工作流程" 
      width="600px"
    >
      <el-form :model="workflowForm" label-width="120px">
        <el-form-item label="工作流程">
          <el-select v-model="workflowForm.workflow_name" placeholder="选择工作流程">
            <el-option 
              v-for="workflow in workflows" 
              :key="workflow.name"
              :label="workflow.name" 
              :value="workflow.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="初始任务数据">
          <el-input 
            v-model="workflowForm.initial_task_json" 
            type="textarea" 
            :rows="8"
            placeholder="请输入JSON格式的初始任务数据"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showWorkflowDialog = false">取消</el-button>
        <el-button type="primary" @click="submitWorkflowExecution" :loading="executing">
          执行
        </el-button>
      </template>
    </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Warning, Operation, Cpu, Refresh, Plus } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { agentApi } from '@/api/agents'

// 响应式数据
const loading = ref(false)
const workflowLoading = ref(false)
const executing = ref(false)
const showAgentDialog = ref(false)
const showWorkflowDialog = ref(false)

const agents = ref([])
const workflows = ref([])

const agentStats = reactive({
  healthy: 0,
  unhealthy: 0,
  total: 0
})

const workflowStats = reactive({
  total: 0
})

const agentForm = reactive({
  agent_name: '',
  task_data_json: ''
})

const workflowForm = reactive({
  workflow_name: '',
  initial_task_json: ''
})

// 方法
const loadAgents = async () => {
  loading.value = true
  try {
    const response = await agentApi.getAgentsInfo()
    if (response.success) {
      agents.value = Object.entries(response.data).map(([key, value]: [string, any]) => ({
        key,
        name: value.name,
        description: value.description,
        required_fields: value.required_fields,
        status: 'healthy' // 默认状态，实际应该从健康检查获取
      }))
      
      agentStats.total = agents.value.length
      agentStats.healthy = agents.value.length
      agentStats.unhealthy = 0
    }
  } catch (error) {
    ElMessage.error('加载智能体信息失败')
  } finally {
    loading.value = false
  }
}

const loadWorkflows = async () => {
  workflowLoading.value = true
  try {
    const response = await agentApi.getWorkflowsInfo()
    if (response.success) {
      workflows.value = Object.entries(response.data).map(([key, value]: [string, any]) => ({
        name: key,
        description: value.description,
        steps: value.steps
      }))
      
      workflowStats.total = workflows.value.length
    }
  } catch (error) {
    ElMessage.error('加载工作流程信息失败')
  } finally {
    workflowLoading.value = false
  }
}

const refreshAgents = async () => {
  await Promise.all([loadAgents(), loadWorkflows()])
  ElMessage.success('状态已刷新')
}

const executeAgent = (agent: any) => {
  agentForm.agent_name = agent.name
  agentForm.task_data_json = JSON.stringify({
    // 根据智能体类型提供示例数据
    test: true
  }, null, 2)
  showAgentDialog.value = true
}

const executeWorkflow = (workflow: any) => {
  workflowForm.workflow_name = workflow.name
  workflowForm.initial_task_json = JSON.stringify({
    // 根据工作流程类型提供示例数据
    test: true
  }, null, 2)
  showWorkflowDialog.value = true
}

const submitAgentExecution = async () => {
  try {
    const taskData = JSON.parse(agentForm.task_data_json)
    executing.value = true
    
    const response = await agentApi.executeAgent({
      agent_name: agentForm.agent_name,
      task_data: taskData
    })
    
    if (response.success) {
      ElMessage.success('智能体执行成功')
      showAgentDialog.value = false
      // 可以显示执行结果
      console.log('执行结果:', response.data)
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('任务数据JSON格式错误')
    } else {
      ElMessage.error('智能体执行失败')
    }
  } finally {
    executing.value = false
  }
}

const submitWorkflowExecution = async () => {
  try {
    const initialTask = JSON.parse(workflowForm.initial_task_json)
    executing.value = true
    
    const response = await agentApi.executeWorkflow({
      workflow_name: workflowForm.workflow_name,
      initial_task: initialTask
    })
    
    if (response.success) {
      ElMessage.success('工作流程执行成功')
      showWorkflowDialog.value = false
      // 可以显示执行结果
      console.log('执行结果:', response.data)
    }
  } catch (error) {
    if (error instanceof SyntaxError) {
      ElMessage.error('初始任务数据JSON格式错误')
    } else {
      ElMessage.error('工作流程执行失败')
    }
  } finally {
    executing.value = false
  }
}

const viewAgentDetails = (agent: any) => {
  ElMessageBox.alert(
    `<strong>智能体详情</strong><br/>
     名称: ${agent.name}<br/>
     描述: ${agent.description}<br/>
     必需字段: ${agent.required_fields.join(', ')}`,
    '智能体详情',
    {
      dangerouslyUseHTMLString: true
    }
  )
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'healthy': return 'success'
    case 'unhealthy': return 'danger'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'healthy': return '健康'
    case 'unhealthy': return '异常'
    default: return '未知'
  }
}

// 生命周期
onMounted(() => {
  loadAgents()
  loadWorkflows()
})
</script>

<style scoped>
.agent-management {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.status-cards {
  margin-bottom: 20px;
}

.status-card {
  height: 100px;
}

.status-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.status-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.status-icon.success { background-color: #67c23a; }
.status-icon.warning { background-color: #e6a23c; }
.status-icon.info { background-color: #909399; }
.status-icon.primary { background-color: #409eff; }

.status-content {
  flex: 1;
}

.status-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.status-label {
  font-size: 14px;
  color: #606266;
  margin-top: 5px;
}

.agents-card, .workflows-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.agent-name {
  display: flex;
  align-items: center;
}

.agent-icon {
  margin-right: 8px;
  color: #409eff;
}

.field-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.workflow-steps {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.step-tag {
  margin-bottom: 5px;
}
</style>
