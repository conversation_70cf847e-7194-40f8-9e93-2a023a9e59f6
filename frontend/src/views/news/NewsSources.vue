<template>
  <Layout>
    <div class="news-sources">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1>资讯源管理</h1>
        <p>管理和配置新闻资讯来源</p>
      </div>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stats-cards">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon success">
                <el-icon><Link /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.total }}</div>
                <div class="stat-label">总数源</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon primary">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.active }}</div>
                <div class="stat-label">活跃源</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon warning">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.inactive }}</div>
                <div class="stat-label">停用源</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-icon info">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ stats.todayArticles }}</div>
                <div class="stat-label">今日文章</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 资讯源列表 -->
      <el-card class="sources-card">
        <template #header>
          <div class="card-header">
            <span>资讯源列表</span>
            <div class="header-actions">
              <el-button @click="refreshSources">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
              <el-button type="primary" @click="showAddDialog = true">
                <el-icon><Plus /></el-icon>
                添加资讯源
              </el-button>
            </div>
          </div>
        </template>

        <!-- 搜索和筛选 -->
        <div class="search-bar">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-input
                v-model="searchQuery"
                placeholder="搜索资讯源名称或URL"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="handleFilter">
                <el-option label="全部" value="" />
                <el-option label="活跃" value="active" />
                <el-option label="停用" value="inactive" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="typeFilter" placeholder="类型筛选" clearable @change="handleFilter">
                <el-option label="全部" value="" />
                <el-option label="RSS" value="rss" />
                <el-option label="网页" value="web" />
                <el-option label="API" value="api" />
              </el-select>
            </el-col>
          </el-row>
        </div>

        <!-- 数据表格 -->
        <el-table :data="filteredSources" v-loading="loading" stripe>
          <el-table-column prop="name" label="名称" width="200">
            <template #default="{ row }">
              <div class="source-name">
                <el-icon class="source-icon"><Notebook /></el-icon>
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="url" label="URL" min-width="300" show-overflow-tooltip />
          <el-table-column label="类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getTypeTagType(row.type)" size="small">
                {{ getTypeText(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="last_crawled" label="最后爬取" width="180">
            <template #default="{ row }">
              {{ formatTime(row.last_crawled) }}
            </template>
          </el-table-column>
          <el-table-column prop="article_count" label="文章数" width="100" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                size="small"
                @click="editSource(row)"
              >
                编辑
              </el-button>
              <el-button
                :type="row.status === 'active' ? 'warning' : 'success'"
                size="small"
                @click="toggleSourceStatus(row)"
              >
                {{ row.status === 'active' ? '停用' : '启用' }}
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteSource(row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>

      <!-- 添加/编辑资讯源对话框 -->
      <el-dialog
        v-model="showAddDialog"
        :title="editingSource ? '编辑资讯源' : '添加资讯源'"
        width="600px"
        @close="resetForm"
      >
        <el-form
          ref="formRef"
          :model="sourceForm"
          :rules="formRules"
          label-width="100px"
        >
          <el-form-item label="名称" prop="name">
            <el-input v-model="sourceForm.name" placeholder="请输入资讯源名称" />
          </el-form-item>
          <el-form-item label="URL" prop="url">
            <el-input v-model="sourceForm.url" placeholder="请输入资讯源URL" />
          </el-form-item>
          <el-form-item label="类型" prop="type">
            <el-select v-model="sourceForm.type" placeholder="请选择类型">
              <el-option label="RSS" value="rss" />
              <el-option label="网页" value="web" />
              <el-option label="API" value="api" />
            </el-select>
          </el-form-item>
          <el-form-item label="描述" prop="description">
            <el-input
              v-model="sourceForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入描述"
            />
          </el-form-item>
          <el-form-item label="爬取间隔" prop="crawl_interval">
            <el-input-number
              v-model="sourceForm.crawl_interval"
              :min="1"
              :max="24"
              controls-position="right"
            />
            <span style="margin-left: 10px;">小时</span>
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-switch
              v-model="sourceForm.status"
              active-value="active"
              inactive-value="inactive"
              active-text="启用"
              inactive-text="停用"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="showAddDialog = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">
            {{ editingSource ? '更新' : '添加' }}
          </el-button>
        </template>
      </el-dialog>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Refresh, Search, Link, CircleCheck, Warning,
  DataAnalysis, Notebook
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { format } from 'date-fns'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showAddDialog = ref(false)
const editingSource = ref<any>(null)
const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')

// 统计数据
const stats = reactive({
  total: 12,
  active: 8,
  inactive: 4,
  todayArticles: 156
})

// 分页数据
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const sourceForm = reactive({
  name: '',
  url: '',
  type: '',
  description: '',
  crawl_interval: 2,
  status: 'active'
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入资讯源名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入资讯源URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择类型', trigger: 'change' }
  ]
}

// 模拟数据
const sources = ref([
  {
    id: 1,
    name: '36氪',
    url: 'https://36kr.com/feed',
    type: 'rss',
    status: 'active',
    description: '36氪科技资讯',
    crawl_interval: 2,
    last_crawled: new Date(),
    article_count: 45
  },
  {
    id: 2,
    name: '虎嗅网',
    url: 'https://www.huxiu.com/rss/0.xml',
    type: 'rss',
    status: 'active',
    description: '虎嗅科技资讯',
    crawl_interval: 3,
    last_crawled: new Date(Date.now() - 3600000),
    article_count: 32
  },
  {
    id: 3,
    name: 'TechCrunch',
    url: 'https://techcrunch.com/feed/',
    type: 'rss',
    status: 'inactive',
    description: 'TechCrunch科技新闻',
    crawl_interval: 4,
    last_crawled: new Date(Date.now() - 86400000),
    article_count: 28
  }
])

// 计算属性
const filteredSources = computed(() => {
  let result = sources.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(source =>
      source.name.toLowerCase().includes(query) ||
      source.url.toLowerCase().includes(query)
    )
  }

  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(source => source.status === statusFilter.value)
  }

  // 类型过滤
  if (typeFilter.value) {
    result = result.filter(source => source.type === typeFilter.value)
  }

  pagination.total = result.length
  return result
})

// 方法
const formatTime = (time: Date | string) => {
  if (!time) return '-'
  const date = typeof time === 'string' ? new Date(time) : time
  return format(date, 'yyyy-MM-dd HH:mm')
}

const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    rss: 'RSS',
    web: '网页',
    api: 'API'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    rss: 'primary',
    web: 'success',
    api: 'warning'
  }
  return typeMap[type] || 'info'
}

const getStatusText = (status: string) => {
  return status === 'active' ? '活跃' : '停用'
}

const getStatusTagType = (status: string) => {
  return status === 'active' ? 'success' : 'danger'
}

const handleSearch = () => {
  pagination.page = 1
}

const handleFilter = () => {
  pagination.page = 1
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
}

const handlePageChange = (page: number) => {
  pagination.page = page
}

const refreshSources = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('刷新成功')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const editSource = (source: any) => {
  editingSource.value = source
  Object.assign(sourceForm, source)
  showAddDialog.value = true
}

const toggleSourceStatus = async (source: any) => {
  try {
    const newStatus = source.status === 'active' ? 'inactive' : 'active'
    source.status = newStatus
    ElMessage.success(`资讯源已${newStatus === 'active' ? '启用' : '停用'}`)
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const deleteSource = async (source: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除资讯源"${source.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = sources.value.findIndex(s => s.id === source.id)
    if (index > -1) {
      sources.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const resetForm = () => {
  editingSource.value = null
  Object.assign(sourceForm, {
    name: '',
    url: '',
    type: '',
    description: '',
    crawl_interval: 2,
    status: 'active'
  })
}

const submitForm = async () => {
  submitting.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (editingSource.value) {
      // 更新现有资讯源
      Object.assign(editingSource.value, sourceForm)
      ElMessage.success('更新成功')
    } else {
      // 添加新资讯源
      const newSource = {
        id: Date.now(),
        ...sourceForm,
        last_crawled: null,
        article_count: 0
      }
      sources.value.push(newSource)
      ElMessage.success('添加成功')
    }

    showAddDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.news-sources {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  height: 100px;
}

.stat-item {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.stat-icon.success { background-color: #67c23a; }
.stat-icon.primary { background-color: #409eff; }
.stat-icon.warning { background-color: #e6a23c; }
.stat-icon.info { background-color: #909399; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  margin-top: 5px;
}

.sources-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-bar {
  margin-bottom: 20px;
}

.source-name {
  display: flex;
  align-items: center;
}

.source-icon {
  margin-right: 8px;
  color: #409eff;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
