<template>
  <Layout>
    <div class="news-sources">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>资讯源管理</span>
            <el-button type="primary">
              <el-icon><Plus /></el-icon>
              添加资讯源
            </el-button>
          </div>
        </template>
        <p>资讯源管理功能开发中...</p>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.news-sources {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
