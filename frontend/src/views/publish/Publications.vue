<template>
  <Layout>
    <div class="publications">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>发布管理</span>
            <el-button type="primary">
              <el-icon><Share /></el-icon>
              新建发布
            </el-button>
          </div>
        </template>
        <p>发布管理功能开发中...</p>
      </el-card>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { Share } from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
</script>

<style scoped>
.publications {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
