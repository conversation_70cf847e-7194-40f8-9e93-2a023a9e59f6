<template>
  <Layout>
    <div class="dashboard">
      <el-card class="welcome-card">
        <h1>欢迎使用AI资讯智能体系统</h1>
        <p>智能化的新闻资讯处理和发布平台</p>
      </el-card>

      <!-- 系统概览 -->
      <el-row :gutter="20" class="overview-cards">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon news">
                <el-icon><Document /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ stats.totalContents }}</div>
                <div class="card-label">总内容数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon sources">
                <el-icon><Link /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ stats.totalSources }}</div>
                <div class="card-label">资讯源数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon published">
                <el-icon><Share /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ stats.totalPublished }}</div>
                <div class="card-label">已发布数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="card-content">
              <div class="card-icon tasks">
                <el-icon><Cpu /></el-icon>
              </div>
              <div class="card-info">
                <div class="card-value">{{ stats.activeTasks }}</div>
                <div class="card-label">活跃任务</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 快速操作 -->
      <el-card class="quick-actions">
        <template #header>
          <span>快速操作</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-button 
              type="primary" 
              size="large" 
              class="action-button"
              @click="startFullPipeline"
            >
              <el-icon><Cpu /></el-icon>
              启动完整流程
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button 
              type="success" 
              size="large" 
              class="action-button"
              @click="crawlAllSources"
            >
              <el-icon><Download /></el-icon>
              爬取所有资讯源
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button 
              type="warning" 
              size="large" 
              class="action-button"
              @click="processNewContent"
            >
              <el-icon><Edit /></el-icon>
              处理新内容
            </el-button>
          </el-col>
          <el-col :span="6">
            <el-button 
              type="info" 
              size="large" 
              class="action-button"
              @click="autoPublish"
            >
              <el-icon><Upload /></el-icon>
              自动发布
            </el-button>
          </el-col>
        </el-row>
      </el-card>

      <!-- 最近任务 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="recent-tasks">
            <template #header>
              <div class="card-header">
                <span>最近任务</span>
                <el-button type="text" @click="$router.push('/task-monitor')">
                  查看全部
                </el-button>
              </div>
            </template>
            <el-table :data="recentTasks" size="small">
              <el-table-column prop="task_type" label="类型" width="120">
                <template #default="{ row }">
                  <el-tag size="small">{{ getTaskTypeText(row.task_type) }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="progress" label="进度" width="100">
                <template #default="{ row }">
                  <el-progress 
                    :percentage="row.progress" 
                    :stroke-width="6"
                    :show-text="false"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间">
                <template #default="{ row }">
                  {{ formatTime(row.created_at) }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="system-status">
            <template #header>
              <span>系统状态</span>
            </template>
            <div class="status-items">
              <div class="status-item">
                <span class="status-label">智能体状态</span>
                <el-tag :type="systemStatus.agents.healthy ? 'success' : 'danger'">
                  {{ systemStatus.agents.healthy ? '正常' : '异常' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">任务队列</span>
                <el-tag :type="systemStatus.queue.status === 'normal' ? 'success' : 'warning'">
                  {{ systemStatus.queue.status === 'normal' ? '正常' : '繁忙' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">数据库连接</span>
                <el-tag :type="systemStatus.database.connected ? 'success' : 'danger'">
                  {{ systemStatus.database.connected ? '已连接' : '断开' }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">Redis缓存</span>
                <el-tag :type="systemStatus.redis.connected ? 'success' : 'danger'">
                  {{ systemStatus.redis.connected ? '已连接' : '断开' }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </Layout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Document, Link, Share, Cpu, Download, Edit, Upload
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { agentApi, taskApi } from '@/api/agents'
import { formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

const router = useRouter()

// 响应式数据
const stats = reactive({
  totalContents: 0,
  totalSources: 0,
  totalPublished: 0,
  activeTasks: 0
})

const recentTasks = ref([])

const systemStatus = reactive({
  agents: { healthy: true },
  queue: { status: 'normal' },
  database: { connected: true },
  redis: { connected: true }
})

// 方法
const loadDashboardData = async () => {
  try {
    // 加载任务统计
    const taskStatsResponse = await taskApi.getTaskStats()
    if (taskStatsResponse.success) {
      const data = taskStatsResponse.data
      stats.activeTasks = data.status_distribution
        .filter(s => ['PENDING', 'RUNNING'].includes(s.status))
        .reduce((sum, s) => sum + s.count, 0)
    }

    // 加载最近任务
    const recentTasksResponse = await taskApi.getTasks({ page: 1, page_size: 5 })
    if (recentTasksResponse.success) {
      recentTasks.value = recentTasksResponse.data.tasks
    }

    // 模拟其他统计数据（实际应该从API获取）
    stats.totalContents = 1250
    stats.totalSources = 15
    stats.totalPublished = 890

  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

const startFullPipeline = async () => {
  try {
    const response = await agentApi.executeWorkflow({
      workflow_name: 'full_pipeline',
      initial_task: {
        source_ids: [1, 2, 3],
        max_articles_per_source: 5
      }
    })
    
    if (response.success) {
      ElMessage.success('完整流程已启动')
      router.push('/task-monitor')
    }
  } catch (error) {
    ElMessage.error('启动完整流程失败')
  }
}

const crawlAllSources = async () => {
  try {
    const response = await agentApi.startCrawlAllSourcesTask({
      max_articles_per_source: 10
    })
    
    if (response.success) {
      ElMessage.success('爬取任务已启动')
      router.push('/task-monitor')
    }
  } catch (error) {
    ElMessage.error('启动爬取任务失败')
  }
}

const processNewContent = async () => {
  try {
    // 这里应该获取待处理的内容ID列表
    const response = await agentApi.startProcessContentTask({
      content_ids: [1, 2, 3, 4, 5]
    })
    
    if (response.success) {
      ElMessage.success('内容处理任务已启动')
      router.push('/task-monitor')
    }
  } catch (error) {
    ElMessage.error('启动内容处理任务失败')
  }
}

const autoPublish = async () => {
  try {
    // 这里应该获取待发布的内容ID列表
    const response = await agentApi.startBatchPublishTask({
      content_ids: [1, 2, 3],
      platform: 'wechat',
      publish_strategy: 'optimal_time'
    })
    
    if (response.success) {
      ElMessage.success('自动发布任务已启动')
      router.push('/task-monitor')
    }
  } catch (error) {
    ElMessage.error('启动自动发布任务失败')
  }
}

const getTaskTypeText = (taskType: string) => {
  const typeMap = {
    'crawl_news': '新闻爬取',
    'rewrite_content': '内容改写',
    'quality_check': '质量检查',
    'publish_content': '内容发布'
  }
  return typeMap[taskType] || taskType
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'SUCCESS': return 'success'
    case 'FAILED': return 'danger'
    case 'RUNNING': return 'warning'
    default: return ''
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'PENDING': return '等待'
    case 'RUNNING': return '运行'
    case 'SUCCESS': return '成功'
    case 'FAILED': return '失败'
    default: return status
  }
}

const formatTime = (timeStr: string) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return formatDistanceToNow(date, { addSuffix: true, locale: zhCN })
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  margin-bottom: 20px;
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.welcome-card h1 {
  font-size: 28px;
  margin-bottom: 10px;
}

.welcome-card p {
  font-size: 16px;
  opacity: 0.9;
}

.overview-cards {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.card-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.card-icon.news { background-color: #409eff; }
.card-icon.sources { background-color: #67c23a; }
.card-icon.published { background-color: #e6a23c; }
.card-icon.tasks { background-color: #f56c6c; }

.card-info {
  flex: 1;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.card-label {
  font-size: 14px;
  color: #606266;
  margin-top: 8px;
}

.quick-actions {
  margin-bottom: 20px;
}

.action-button {
  width: 100%;
  height: 60px;
  font-size: 16px;
}

.recent-tasks, .system-status {
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-items {
  padding: 20px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-weight: 500;
  color: #303133;
}
</style>
