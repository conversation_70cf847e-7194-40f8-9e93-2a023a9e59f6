import request from './request'
import type { LoginRequest, LoginResponse, User } from '@/types/user'

export const authApi = {
  // 登录
  login: (data: LoginRequest): Promise<LoginResponse> => {
    return request.post('/auth/login', data)
  },

  // 获取用户信息
  getUserInfo: (): Promise<User> => {
    return request.get('/auth/me')
  },

  // 刷新token
  refreshToken: (): Promise<{ access_token: string }> => {
    return request.post('/auth/refresh')
  },

  // 修改密码
  changePassword: (data: { old_password: string; new_password: string }) => {
    return request.post('/auth/change-password', data)
  }
}
