import request from './request'
import type { LoginRequest, LoginResponse, User } from '@/types/user'

export const authApi = {
  // 登录 - 调用真实后端API
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    try {
      const response = await request.post('/auth/login', data)

      // 转换后端响应格式为前端期望的格式
      return {
        access_token: response.access_token,
        token_type: response.token_type || 'bearer',
        user: {
          id: response.user_id,
          username: response.username,
          email: '<EMAIL>', // 后端暂时没有返回email
          role: 'admin',
          is_superuser: true
        }
      }
    } catch (error: any) {
      // 处理后端错误响应
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail)
      }
      throw new Error('登录失败，请检查网络连接')
    }
  },

  // 获取用户信息 - 调用真实后端API
  getUserInfo: async (): Promise<User> => {
    try {
      const response = await request.get('/auth/me')
      return {
        id: response.user_id,
        username: response.username,
        email: response.email || '<EMAIL>',
        role: 'admin',
        is_superuser: true
      }
    } catch (error) {
      throw new Error('获取用户信息失败')
    }
  },

  // 刷新token - 模拟API
  refreshToken: async (): Promise<{ access_token: string }> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      access_token: 'new-mock-jwt-token'
    }
  },

  // 修改密码 - 模拟API
  changePassword: async (data: { old_password: string; new_password: string }) => {
    await new Promise(resolve => setTimeout(resolve, 800))
    return { message: '密码修改成功' }
  },

  // 登出 - 调用真实后端API
  logout: async (): Promise<void> => {
    try {
      await request.post('/auth/logout')
    } catch (error) {
      // 登出失败也不抛出错误，因为前端会清除本地状态
      console.warn('后端登出失败，但前端状态已清除')
    }
  }
}
