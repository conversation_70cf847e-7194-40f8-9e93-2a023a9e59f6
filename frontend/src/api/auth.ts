import request from './request'
import type { LoginRequest, LoginResponse, User } from '@/types/user'

export const authApi = {
  // 登录 - 模拟API
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟登录验证
    if (data.username === 'admin' && data.password === 'admin123') {
      return {
        access_token: 'mock-jwt-token',
        token_type: 'bearer',
        user: {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          is_superuser: true
        }
      }
    } else {
      throw new Error('用户名或密码错误')
    }
  },

  // 获取用户信息 - 模拟API
  getUserInfo: async (): Promise<User> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      is_superuser: true
    }
  },

  // 刷新token - 模拟API
  refreshToken: async (): Promise<{ access_token: string }> => {
    await new Promise(resolve => setTimeout(resolve, 500))
    return {
      access_token: 'new-mock-jwt-token'
    }
  },

  // 修改密码 - 模拟API
  changePassword: async (data: { old_password: string; new_password: string }) => {
    await new Promise(resolve => setTimeout(resolve, 800))
    return { message: '密码修改成功' }
  },

  // 登出 - 模拟API
  logout: async (): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 300))
    // 模拟登出成功
  }
}
