/**
 * 智能体相关API
 */
import request from './request'

export interface AgentExecuteRequest {
  agent_name: string
  task_data: Record<string, any>
}

export interface WorkflowExecuteRequest {
  workflow_name: string
  initial_task: Record<string, any>
}

export interface BatchAgentRequest {
  agent_name: string
  tasks: Record<string, any>[]
}

export interface CrawlSourceRequest {
  source_id: number
  max_articles?: number
}

export interface CrawlAllSourcesRequest {
  max_articles_per_source?: number
}

export interface ProcessContentRequest {
  content_ids: number[]
}

export interface RewriteContentRequest {
  content_id: number
  style?: string
  target_length?: string
}

export interface QualityCheckRequest {
  content_id: number
  check_level?: string
}

export interface PublishContentRequest {
  content_id: number
  platform?: string
  publish_strategy?: string
  scheduled_time?: string
}

export interface BatchPublishRequest {
  content_ids: number[]
  platform?: string
  publish_strategy?: string
}

export const agentApi = {
  // 智能体信息
  getAgentsInfo() {
    return request.get('/ai-agents/agents/info')
  },

  getAgentInfo(agentName: string) {
    return request.get(`/ai-agents/agents/${agentName}/info`)
  },

  getWorkflowsInfo() {
    return request.get('/ai-agents/workflows/info')
  },

  getWorkflowInfo(workflowName: string) {
    return request.get(`/ai-agents/workflows/${workflowName}/info`)
  },

  // 智能体执行
  executeAgent(data: AgentExecuteRequest) {
    return request.post('/ai-agents/agents/execute', data)
  },

  batchExecuteAgent(data: BatchAgentRequest) {
    return request.post('/ai-agents/agents/batch-execute', data)
  },

  executeWorkflow(data: WorkflowExecuteRequest) {
    return request.post('/ai-agents/workflows/execute', data)
  },

  // 异步任务
  startCrawlSourceTask(data: CrawlSourceRequest) {
    return request.post('/ai-agents/tasks/crawl-source', data)
  },

  startCrawlAllSourcesTask(data: CrawlAllSourcesRequest) {
    return request.post('/ai-agents/tasks/crawl-all-sources', data)
  },

  startProcessContentTask(data: ProcessContentRequest) {
    return request.post('/ai-agents/tasks/process-content', data)
  },

  startRewriteContentTask(data: RewriteContentRequest) {
    return request.post('/ai-agents/tasks/rewrite-content', data)
  },

  startQualityCheckTask(data: QualityCheckRequest) {
    return request.post('/ai-agents/tasks/quality-check', data)
  },

  startPublishContentTask(data: PublishContentRequest) {
    return request.post('/ai-agents/tasks/publish-content', data)
  },

  startBatchPublishTask(data: BatchPublishRequest) {
    return request.post('/ai-agents/tasks/batch-publish', data)
  },

  // 健康检查
  healthCheck() {
    return request.get('/ai-agents/health')
  }
}

// 任务管理API
export interface TaskListParams {
  page?: number
  page_size?: number
  status?: string
  task_type?: string
}

export const taskApi = {
  // 任务列表
  getTasks(params: TaskListParams = {}) {
    return request.get('/task-management/tasks', { params })
  },

  getTask(taskId: string) {
    return request.get(`/task-management/tasks/${taskId}`)
  },

  getCeleryTaskInfo(taskId: string) {
    return request.get(`/task-management/tasks/${taskId}/celery-info`)
  },

  // 任务控制
  cancelTask(taskId: string) {
    return request.post(`/task-management/tasks/${taskId}/cancel`)
  },

  deleteTask(taskId: string) {
    return request.delete(`/task-management/tasks/${taskId}`)
  },

  cleanupOldTasks(days: number = 30) {
    return request.post('/task-management/tasks/cleanup', { days })
  },

  // 任务统计
  getTaskStats() {
    return request.get('/task-management/tasks/stats/summary')
  },

  getActiveTasksCount() {
    return request.get('/task-management/tasks/active/count')
  },

  // Celery信息
  getCeleryWorkers() {
    return request.get('/task-management/celery/workers')
  },

  getCeleryQueues() {
    return request.get('/task-management/celery/queues')
  }
}

// 智能体工作流程预设
export const workflowPresets = {
  // 完整新闻处理流程
  full_pipeline: {
    name: 'full_pipeline',
    description: '完整的新闻处理流程：爬取 → 改写 → 质检 → 发布',
    example_data: {
      source_ids: [1, 2, 3],
      max_articles_per_source: 5,
      rewrite_style: 'wechat_official',
      quality_threshold: 0.7,
      publish_platform: 'wechat',
      publish_strategy: 'optimal_time'
    }
  },

  // 仅内容处理
  content_only: {
    name: 'content_only',
    description: '仅处理已有内容：改写 → 质检',
    example_data: {
      content_ids: [1, 2, 3],
      rewrite_style: 'wechat_official',
      target_length: 'medium',
      quality_check_level: 'standard'
    }
  },

  // 仅爬取
  scraping_only: {
    name: 'scraping_only',
    description: '仅爬取新闻内容',
    example_data: {
      source_ids: [1, 2, 3],
      max_articles_per_source: 10,
      save_original: true
    }
  },

  // 仅发布
  publish_only: {
    name: 'publish_only',
    description: '仅发布已审核内容',
    example_data: {
      content_ids: [1, 2, 3],
      platform: 'wechat',
      publish_strategy: 'immediate',
      batch_interval: 30
    }
  }
}

// 智能体任务数据模板
export const agentTaskTemplates = {
  news_scraping: {
    name: 'NewsScrapingAgent',
    description: '新闻爬取智能体',
    example_data: {
      source_config: {
        url: 'https://example.com/news',
        max_articles: 10,
        category: 'AI技术'
      },
      filters: {
        keywords: ['人工智能', '机器学习'],
        min_length: 100,
        exclude_keywords: ['广告', '推广']
      }
    }
  },

  content_rewrite: {
    name: 'ContentRewriteAgent',
    description: '内容改写智能体',
    example_data: {
      original_content: {
        title: '原始标题',
        content: '原始内容...',
        url: 'https://example.com/article',
        tags: ['AI', '技术']
      },
      style: 'wechat_official',
      target_length: 'medium',
      preserve_facts: true
    }
  },

  quality_control: {
    name: 'QualityControlAgent',
    description: '质量控制智能体',
    example_data: {
      content: {
        title: '文章标题',
        content: '文章内容...',
        summary: '文章摘要',
        tags: ['AI', '技术']
      },
      original_content: {
        title: '原始标题',
        content: '原始内容...'
      },
      check_level: 'standard'
    }
  },

  publish: {
    name: 'PublishAgent',
    description: '发布智能体',
    example_data: {
      content: {
        id: 1,
        title: '文章标题',
        content: '文章内容...',
        summary: '文章摘要',
        tags: ['AI', '技术'],
        keywords: ['人工智能', '机器学习']
      },
      platform: 'wechat',
      publish_strategy: 'immediate',
      publish_config: {
        source_attribution: '来源网站',
        category: 'AI技术'
      }
    }
  }
}
