#!/usr/bin/env python3
"""
前端构建脚本
"""

import subprocess
import sys
import os
import shutil

def run_command(command, cwd=None, description=""):
    """运行命令"""
    try:
        print(f"执行: {description or command}")
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True,
            encoding='utf-8'
        )
        
        if result.stdout:
            print("输出:", result.stdout)
        if result.stderr and result.returncode != 0:
            print("错误:", result.stderr)
            
        return result.returncode == 0
    except Exception as e:
        print(f"命令执行失败: {str(e)}")
        return False

def check_node():
    """检查Node.js环境"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Node.js 版本: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Node.js 未安装")
    return False

def check_npm():
    """检查npm"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ npm 版本: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ npm 未安装")
    return False

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 前端构建器")
    print("=" * 50)
    
    # 检查环境
    if not check_node() or not check_npm():
        print("请先安装Node.js和npm")
        return False
    
    # 检查前端目录
    frontend_dir = "frontend"
    if not os.path.exists(frontend_dir):
        print(f"❌ 前端目录不存在: {frontend_dir}")
        return False
    
    print(f"✓ 前端目录: {frontend_dir}")
    
    # 清理旧的构建文件
    dist_dir = os.path.join(frontend_dir, "dist")
    if os.path.exists(dist_dir):
        print("清理旧的构建文件...")
        shutil.rmtree(dist_dir)
    
    # 检查依赖
    node_modules = os.path.join(frontend_dir, "node_modules")
    if not os.path.exists(node_modules):
        print("安装前端依赖...")
        if not run_command("npm install", cwd=frontend_dir, description="安装依赖"):
            print("❌ 依赖安装失败")
            return False
    else:
        print("✓ 前端依赖已安装")
    
    # 尝试不同的构建方式
    print("\n开始构建前端...")
    
    # 方式1: 直接使用vite build（跳过类型检查）
    print("尝试方式1: 直接构建（跳过类型检查）")
    if run_command("npm run build", cwd=frontend_dir, description="构建前端"):
        print("✅ 前端构建成功!")
        print(f"构建文件位置: {dist_dir}")
        return True
    
    # 方式2: 使用带类型检查的构建
    print("\n尝试方式2: 带类型检查的构建")
    if run_command("npm run build-with-check", cwd=frontend_dir, description="带类型检查构建"):
        print("✅ 前端构建成功!")
        print(f"构建文件位置: {dist_dir}")
        return True
    
    # 方式3: 强制构建（忽略错误）
    print("\n尝试方式3: 强制构建")
    if run_command("npx vite build", cwd=frontend_dir, description="强制构建"):
        print("✅ 前端构建成功!")
        print(f"构建文件位置: {dist_dir}")
        return True
    
    print("❌ 所有构建方式都失败了")
    return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 构建完成! 可以使用以下方式部署:")
        print("1. 将 frontend/dist 目录部署到Web服务器")
        print("2. 或运行 'npm run preview' 预览构建结果")
    else:
        print("\n❌ 构建失败，请检查错误信息")
        sys.exit(1)
