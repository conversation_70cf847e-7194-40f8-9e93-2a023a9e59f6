#!/usr/bin/env python3
"""
只安装后端依赖的脚本
适用于没有Node.js环境或只需要后端功能的情况
"""

import os
import sys
import subprocess
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"

def run_command(command, cwd=None, check=True):
    """运行命令并处理错误"""
    print(f"执行命令: {' '.join(command) if isinstance(command, list) else command}")
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=isinstance(command, str)
        )
        if result.stdout:
            print(result.stdout)
        return result
    except FileNotFoundError as e:
        print(f"命令未找到: {' '.join(command) if isinstance(command, list) else command}")
        print(f"错误: {e}")
        return None
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            return None
        return e

def install_core_dependencies():
    """安装核心依赖"""
    print("=" * 50)
    print("安装核心后端依赖")
    print("=" * 50)
    
    # 核心依赖列表
    core_deps = [
        "fastapi",
        "uvicorn[standard]", 
        "pydantic",
        "redis",
        "celery",
        "python-dotenv",
        "loguru",
        "httpx",
        "requests",
        "beautifulsoup4",
        "python-multipart",
        "python-jose[cryptography]",
        "passlib[bcrypt]"
    ]
    
    failed_packages = []
    
    print("安装核心Web和任务处理依赖...")
    for dep in core_deps:
        result = run_command([
            sys.executable, "-m", "pip", "install", dep
        ], cwd=BACKEND_DIR, check=False)
        
        if result is None or result.returncode != 0:
            print(f"⚠️  {dep} 安装失败，跳过...")
            failed_packages.append(dep)
        else:
            print(f"✓ {dep} 安装成功")
    
    # 尝试安装AI相关依赖（可选）
    ai_deps = ["smolagents", "transformers"]
    print("\n安装AI相关依赖（可选）...")
    for dep in ai_deps:
        result = run_command([
            sys.executable, "-m", "pip", "install", dep
        ], cwd=BACKEND_DIR, check=False)
        
        if result is None or result.returncode != 0:
            print(f"⚠️  {dep} 安装失败，AI功能可能受限")
            failed_packages.append(dep)
        else:
            print(f"✓ {dep} 安装成功")
    
    if failed_packages:
        print(f"\n⚠️  以下包安装失败:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
        print("\n系统仍可运行基本功能")
    else:
        print("\n✅ 所有依赖安装完成!")
    
    return len(failed_packages) == 0

def create_env_file():
    """创建环境配置文件"""
    print("\n" + "=" * 50)
    print("创建环境配置文件")
    print("=" * 50)
    
    env_file = BACKEND_DIR / ".env"
    env_example = BACKEND_DIR / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        import shutil
        shutil.copy2(env_example, env_file)
        print("✅ 环境配置文件已创建: backend/.env")
        print("⚠️  请编辑该文件配置数据库连接等信息")
    else:
        print("ℹ️  环境配置文件已存在")

def test_installation():
    """测试安装是否成功"""
    print("\n" + "=" * 50)
    print("测试安装")
    print("=" * 50)
    
    test_packages = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('celery', 'Celery'),
        ('redis', 'Redis')
    ]
    
    all_success = True
    for package, name in test_packages:
        try:
            __import__(package)
            print(f"✓ {name} 导入成功")
        except ImportError:
            print(f"✗ {name} 导入失败")
            all_success = False
    
    return all_success

def main():
    """主函数"""
    print("AI资讯智能体系统 - 后端依赖安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 升级pip
    print("\n升级pip...")
    run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=False)
    
    # 安装依赖
    success = install_core_dependencies()
    
    # 创建环境配置文件
    create_env_file()
    
    # 测试安装
    test_success = test_installation()
    
    print("\n" + "=" * 60)
    if test_success:
        print("✅ 后端安装完成!")
    else:
        print("⚠️  后端安装完成，但部分功能可能受限")
    print("=" * 60)
    print("接下来的步骤:")
    print("1. 启动Redis服务: redis-server")
    print("2. 启动后端API: cd backend && uvicorn app.main:app --reload")
    print("3. 访问API文档: http://localhost:8000/docs")
    print("\n如需完整功能，请:")
    print("1. 安装Node.js: https://nodejs.org/")
    print("2. 安装前端: cd frontend && npm install")
    print("3. 启动前端: cd frontend && npm run dev")
    print("=" * 60)

if __name__ == "__main__":
    main()
