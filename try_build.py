#!/usr/bin/env python3
"""
尝试构建并捕获错误
"""

import subprocess
import re

def try_build():
    """尝试构建"""
    print("🚀 尝试构建前端...")
    
    try:
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="frontend",
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=60
        )
        
        print("构建输出:")
        print(result.stdout)
        
        if result.stderr:
            print("构建错误/警告:")
            print(result.stderr)
            
            # 分析错误信息
            if "is not exported by" in result.stderr:
                # 提取图标名称
                icon_match = re.search(r'"([^"]+)" is not exported by.*icons-vue', result.stderr)
                if icon_match:
                    missing_icon = icon_match.group(1)
                    print(f"\n❌ 发现缺失图标: {missing_icon}")
                    
                    # 提取文件信息
                    file_match = re.search(r'imported by "([^"]+)"', result.stderr)
                    if file_match:
                        problem_file = file_match.group(1)
                        print(f"📄 问题文件: {problem_file}")
                        
                        # 建议替换
                        suggestions = {
                            'Play': 'Cpu',
                            'Pause': 'Monitor', 
                            'Stop': 'Monitor',
                            'Robot': 'Cpu',
                            'Record': 'Document',
                            'VideoPlay': 'Document',
                            'VideoPause': 'Document'
                        }
                        
                        if missing_icon in suggestions:
                            print(f"💡 建议替换: {missing_icon} → {suggestions[missing_icon]}")
                        else:
                            print(f"💡 建议替换: {missing_icon} → Cpu 或 Monitor")
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            return True
        else:
            print(f"❌ 构建失败，退出码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 构建超时")
        return False
    except Exception as e:
        print(f"❌ 构建出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = try_build()
    if success:
        print("\n🎉 构建成功!")
        print("现在可以运行: python start_full_system.py")
    else:
        print("\n❌ 构建失败，请根据上述信息修复问题")
