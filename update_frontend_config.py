#!/usr/bin/env python3
"""
更新前端配置指向新的后端端口
"""

import os
import re

def update_vite_config():
    """更新Vite配置"""
    print("🔧 更新Vite配置...")
    
    config_file = "frontend/vite.config.ts"
    
    if not os.path.exists(config_file):
        print("❌ Vite配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换端口8000为8001
        updated_content = content.replace(
            'target: \'http://localhost:8000\'',
            'target: \'http://127.0.0.1:8001\''
        )
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print("✅ Vite配置已更新")
        return True
        
    except Exception as e:
        print(f"❌ 更新Vite配置失败: {str(e)}")
        return False

def create_env_file():
    """创建环境变量文件"""
    print("🔧 创建环境变量文件...")
    
    env_content = """# API配置
VITE_API_BASE_URL=http://127.0.0.1:8001/api/v1
VITE_APP_TITLE=AI资讯智能体
"""
    
    try:
        with open("frontend/.env", 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("✅ 环境变量文件已创建")
        return True
        
    except Exception as e:
        print(f"❌ 创建环境变量文件失败: {str(e)}")
        return False

def start_frontend_dev():
    """启动前端开发服务器"""
    print("🚀 启动前端开发服务器...")
    
    import subprocess
    import sys
    
    try:
        cmd = ["npm", "run", "dev", "--", "--host", "127.0.0.1", "--port", "5173"]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("在frontend目录中启动...")
        
        process = subprocess.Popen(
            cmd,
            cwd="frontend"
        )
        
        print("✅ 前端开发服务器启动中...")
        print("📍 前端地址: http://127.0.0.1:5173")
        print("📍 后端地址: http://127.0.0.1:8001")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动前端失败: {str(e)}")
        return False

def build_and_serve():
    """构建前端并通过后端服务"""
    print("🔧 构建前端...")
    
    import subprocess
    
    try:
        # 构建前端
        print("正在构建前端...")
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="frontend",
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ 前端构建成功")
            
            # 检查dist目录
            if os.path.exists("frontend/dist"):
                print("✅ 构建文件存在")
                print("📍 现在可以直接访问: http://127.0.0.1:8001")
                print("   (后端会自动服务前端静态文件)")
                return True
            else:
                print("❌ 构建文件不存在")
                return False
        else:
            print("❌ 前端构建失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建前端失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 前端配置更新")
    print("=" * 50)
    
    # 更新配置
    update_vite_config()
    create_env_file()
    
    print("\n选择运行模式:")
    print("1. 开发模式 (启动前端开发服务器)")
    print("2. 生产模式 (构建前端，通过后端访问)")
    print("3. 仅更新配置")
    
    choice = input("\n请选择 (1-3): ").strip()
    
    if choice == "1":
        if start_frontend_dev():
            print("\n🎉 开发模式启动完成!")
            print("📍 前端: http://127.0.0.1:5173")
            print("📍 后端: http://127.0.0.1:8001")
    elif choice == "2":
        if build_and_serve():
            print("\n🎉 生产模式配置完成!")
            print("📍 访问地址: http://127.0.0.1:8001")
    else:
        print("\n✅ 配置更新完成!")
        print("手动启动前端: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
