#!/usr/bin/env python3
"""
完整系统启动脚本 - 同时启动前端和后端
"""

import subprocess
import sys
import os
import time
import threading
import signal

class SystemStarter:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
    
    def run_command_check(self, command, description):
        """运行命令并检查结果"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                encoding='utf-8'
            )
            if result.returncode == 0:
                print(f"✓ {description}")
                return True
            else:
                print(f"❌ {description} 失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ {description} 出错: {str(e)}")
            return False
    
    def check_dependencies(self):
        """检查系统依赖"""
        print("检查系统依赖...")
        print("-" * 30)
        
        # 检查Python
        if not self.run_command_check("python --version", "Python 检查"):
            return False
        
        # 检查Node.js
        if not self.run_command_check("node --version", "Node.js 检查"):
            print("请先安装Node.js: https://nodejs.org/")
            return False
        
        # 检查npm
        if not self.run_command_check("npm --version", "npm 检查"):
            return False
        
        return True
    
    def start_backend(self):
        """启动后端服务"""
        print("\n启动后端服务...")
        try:
            self.backend_process = subprocess.Popen(
                [sys.executable, "start_backend_only.py"],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8'
            )
            
            # 等待后端启动
            time.sleep(3)
            
            if self.backend_process.poll() is None:
                print("✓ 后端服务启动成功")
                print("  API地址: http://localhost:8000")
                print("  API文档: http://localhost:8000/docs")
                return True
            else:
                print("❌ 后端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 后端启动出错: {str(e)}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("\n启动前端服务...")
        
        # 检查前端目录
        if not os.path.exists("frontend"):
            print("❌ 前端目录不存在")
            return False
        
        # 检查并安装前端依赖
        if not os.path.exists("frontend/node_modules"):
            print("安装前端依赖...")
            if not self.run_command_check("cd frontend && npm install", "前端依赖安装"):
                return False
        
        try:
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd="frontend",
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8'
            )
            
            # 等待前端启动
            time.sleep(5)
            
            if self.frontend_process.poll() is None:
                print("✓ 前端服务启动成功")
                print("  前端地址: http://localhost:5173")
                return True
            else:
                print("❌ 前端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 前端启动出错: {str(e)}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(5)
            
            # 检查后端进程
            if self.backend_process and self.backend_process.poll() is not None:
                print("⚠️ 后端服务意外停止")
                self.running = False
                break
            
            # 检查前端进程
            if self.frontend_process and self.frontend_process.poll() is not None:
                print("⚠️ 前端服务意外停止")
                self.running = False
                break
    
    def stop_services(self):
        """停止所有服务"""
        print("\n正在停止服务...")
        self.running = False
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✓ 后端服务已停止")
            except:
                self.backend_process.kill()
                print("✓ 后端服务已强制停止")
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✓ 前端服务已停止")
            except:
                self.frontend_process.kill()
                print("✓ 前端服务已强制停止")
    
    def run(self):
        """运行完整系统"""
        print("=" * 60)
        print("AI资讯智能体系统 - 完整系统启动器")
        print("=" * 60)
        
        # 检查依赖
        if not self.check_dependencies():
            return
        
        # 启动后端
        if not self.start_backend():
            return
        
        # 启动前端
        if not self.start_frontend():
            self.stop_services()
            return
        
        print("\n" + "=" * 60)
        print("🚀 系统启动完成!")
        print("=" * 60)
        print("访问地址:")
        print("  🌐 前端界面: http://localhost:5173")
        print("  📱 API文档: http://localhost:8000/docs")
        print("  🔍 健康检查: http://localhost:8000/health")
        print("\n登录信息:")
        print("  👤 用户名: admin")
        print("  🔑 密码: admin123")
        print("\n按 Ctrl+C 停止所有服务")
        print("=" * 60)
        
        # 设置信号处理
        def signal_handler(signum, frame):
            self.stop_services()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 监控进程
        try:
            self.monitor_processes()
        except KeyboardInterrupt:
            pass
        finally:
            self.stop_services()

def main():
    starter = SystemStarter()
    starter.run()

if __name__ == "__main__":
    main()
