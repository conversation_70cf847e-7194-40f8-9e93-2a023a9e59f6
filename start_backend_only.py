#!/usr/bin/env python3
"""
仅启动后端API的脚本
适用于调试或只需要API功能的情况
"""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path

# 获取项目路径
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"

# 全局进程列表
processes = []

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n正在关闭服务...")
    for process in processes:
        if process.poll() is None:  # 进程仍在运行
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
    print("所有服务已关闭")
    sys.exit(0)

def check_dependencies():
    """检查基本依赖"""
    print("检查系统依赖...")
    
    # 检查Python包
    required_packages = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('redis', 'Redis'),
    ]
    
    missing_packages = []
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✓ {name} 已安装")
        except ImportError:
            print(f"✗ {name} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下包: {', '.join(missing_packages)}")
        print("请运行: python fix_dependencies.py")
        return False
    
    # 检查Redis连接
    try:
        import redis
        r = redis.Redis(
            host='localhost', 
            port=6379, 
            db=0,
            username='default',
            password='000001'
        )
        r.ping()
        print("✓ Redis连接正常（使用认证）")
    except redis.AuthenticationError:
        print("✗ Redis认证失败，请检查用户名和密码")
        print("配置的用户名: default, 密码: 000001")
        return False
    except redis.ConnectionError:
        print("✗ Redis连接失败，请确保Redis服务正在运行")
        print("可以运行: python start_redis.py")
        return False
    except Exception as e:
        print(f"✗ Redis连接失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置加载"""
    print("测试配置文件...")
    try:
        # 切换到backend目录并添加到Python路径
        os.chdir(BACKEND_DIR)
        sys.path.insert(0, str(BACKEND_DIR))
        
        from app.core.config import settings
        print("✓ 配置文件加载成功")
        return True
    except Exception as e:
        print(f"✗ 配置文件加载失败: {e}")
        print("请运行: python fix_dependencies.py")
        return False

def start_backend_api():
    """启动后端API服务"""
    print("启动后端API服务...")
    os.chdir(BACKEND_DIR)
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        "PYTHONPATH": str(BACKEND_DIR),
        "ENVIRONMENT": "development"
    })
    
    # 启动FastAPI应用
    process = subprocess.Popen([
        sys.executable, "-m", "uvicorn", 
        "app.main:app", 
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ], env=env)
    
    processes.append(process)
    print("✓ 后端API服务已启动")
    print("  API地址: http://localhost:8000")
    print("  API文档: http://localhost:8000/docs")
    print("  健康检查: http://localhost:8000/health")
    return process

def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    print("=" * 50)
    print("AI资讯智能体系统 - 后端启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("依赖检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    # 测试配置
    if not test_config():
        print("配置测试失败，请解决上述问题后重试")
        sys.exit(1)
    
    print("\n开始启动后端服务...")
    
    # 启动后端API
    api_process = start_backend_api()
    
    if api_process:
        print("\n" + "=" * 50)
        print("🚀 后端服务启动完成!")
        print("=" * 50)
        print("访问地址:")
        print("  📱 API文档: http://localhost:8000/docs")
        print("  🔍 健康检查: http://localhost:8000/health")
        print("  📊 API接口: http://localhost:8000/api/v1/")
        print("\n按 Ctrl+C 停止服务")
        print("=" * 50)
        
        try:
            # 等待进程结束
            api_process.wait()
        except KeyboardInterrupt:
            signal_handler(signal.SIGINT, None)
    else:
        print("❌ 后端服务启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
