#!/usr/bin/env python3
"""
Redis启动脚本
配置Redis认证：用户名default，密码000001
"""

import os
import sys
import subprocess
import time
from pathlib import Path

PROJECT_ROOT = Path(__file__).parent

def check_redis_installed():
    """检查Redis是否已安装"""
    try:
        result = subprocess.run(["redis-server", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Redis已安装: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Redis未安装")
    print("\n安装Redis:")
    print("Windows: 下载Redis for Windows")
    print("  https://github.com/microsoftarchive/redis/releases")
    print("macOS: brew install redis")
    print("Ubuntu: sudo apt install redis-server")
    print("CentOS: sudo yum install redis")
    return False

def create_redis_config():
    """创建Redis配置文件"""
    config_content = """# Redis配置文件 - AI资讯智能体系统
port 6379
bind 127.0.0.1
timeout 0
tcp-keepalive 300

# 内存配置
maxmemory 256mb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice

# 安全配置 - 使用简单密码认证
requirepass 000001

# 数据库数量
databases 16

# 客户端连接配置
maxclients 10000

# 网络配置
tcp-backlog 511

# 其他配置
always-show-logo yes
"""
    
    config_file = PROJECT_ROOT / "redis-auth.conf"
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✓ Redis配置文件已创建: {config_file}")
    return config_file

def start_redis_server():
    """启动Redis服务器"""
    if not check_redis_installed():
        return False
    
    # 创建配置文件
    config_file = create_redis_config()
    
    print("\n启动Redis服务器...")
    print("配置信息:")
    print("  端口: 6379")
    print("  密码: 000001")
    print("  用户名: default (Redis 6.0+)")
    
    try:
        # 启动Redis服务器
        process = subprocess.Popen([
            "redis-server", str(config_file)
        ])
        
        # 等待启动
        time.sleep(2)
        
        # 测试连接
        test_result = test_redis_connection()
        if test_result:
            print("✓ Redis服务器启动成功!")
            print(f"进程ID: {process.pid}")
            print("\n要停止Redis服务器，请按Ctrl+C")
            
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\n正在停止Redis服务器...")
                process.terminate()
                process.wait()
                print("✓ Redis服务器已停止")
        else:
            print("❌ Redis启动失败")
            process.terminate()
            return False
            
    except FileNotFoundError:
        print("❌ 找不到redis-server命令")
        return False
    except Exception as e:
        print(f"❌ 启动Redis失败: {e}")
        return False
    
    return True

def test_redis_connection():
    """测试Redis连接"""
    try:
        import redis
        
        # 测试无认证连接（应该失败）
        try:
            r_no_auth = redis.Redis(host='localhost', port=6379, db=0)
            r_no_auth.ping()
            print("⚠️  Redis未启用认证")
        except redis.AuthenticationError:
            print("✓ Redis认证已启用")
        except redis.ConnectionError:
            print("❌ Redis连接失败")
            return False
        
        # 测试认证连接
        r = redis.Redis(
            host='localhost', 
            port=6379, 
            db=0,
            password='000001'
        )
        r.ping()
        print("✓ Redis认证连接成功")
        
        # 测试基本操作
        r.set('test_key', 'test_value')
        value = r.get('test_key')
        if value == b'test_value':
            print("✓ Redis读写测试成功")
            r.delete('test_key')
        
        return True
        
    except ImportError:
        print("❌ redis-py未安装，请运行: pip install redis")
        return False
    except Exception as e:
        print(f"❌ Redis连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Redis启动脚本 - AI资讯智能体系统")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 仅测试连接
        print("测试Redis连接...")
        if test_redis_connection():
            print("✓ Redis连接正常")
        else:
            print("❌ Redis连接失败")
            sys.exit(1)
    else:
        # 启动Redis服务器
        start_redis_server()

if __name__ == "__main__":
    main()
