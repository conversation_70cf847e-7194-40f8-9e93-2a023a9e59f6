# AI资讯智能体系统

基于smolagents框架的智能化新闻资讯处理和发布平台，支持自动爬取、内容改写、质量检查和多平台发布。

## 系统架构

### 后端技术栈
- **框架**: FastAPI + Celery + Redis + PostgreSQL + MongoDB + MinIO
- **AI智能体**: smolagents框架 (Hugging Face)
- **异步处理**: Celery任务队列
- **数据库**: PostgreSQL (关系型数据) + MongoDB (文档存储)
- **文件存储**: MinIO对象存储
- **缓存**: Redis

### 前端技术栈
- **框架**: Vue.js 3 + Element Plus + Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **实时通信**: Socket.IO

### AI智能体架构
- **NewsScrapingAgent**: 新闻爬取智能体
- **ContentRewriteAgent**: 内容改写智能体
- **QualityControlAgent**: 质量控制智能体
- **PublishAgent**: 发布智能体
- **AgentManager**: 智能体管理器

## 功能特性

### 核心功能
- ✅ 多源新闻自动爬取
- ✅ AI驱动的内容改写和优化
- ✅ 多维度内容质量评估
- ✅ 多平台自动发布 (微信公众号、微博、今日头条)
- ✅ 智能体工作流程管理
- ✅ 实时任务监控和管理

### 管理功能
- ✅ 资讯源管理
- ✅ 内容管理和编辑
- ✅ 发布计划和策略
- ✅ 系统监控和统计
- ✅ 用户权限管理

## 快速开始

### 环境要求
- Python 3.8+
- Node.js 18+
- Redis 6+
- PostgreSQL 12+
- MongoDB 4.4+

### 安装依赖

#### 后端依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 前端依赖
```bash
cd frontend
npm install
```

### 配置环境

1. 复制环境配置文件：
```bash
cp backend/.env.example backend/.env
```

2. 编辑 `backend/.env` 文件，配置数据库连接等信息

3. 初始化数据库：
```bash
cd backend
python -m alembic upgrade head
```

### 启动系统

#### 方式一：使用启动脚本（推荐）
```bash
python start_system.py
```

#### 方式二：手动启动各服务

1. 启动Redis：
```bash
redis-server
```

2. 启动后端API：
```bash
cd backend
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

3. 启动Celery Worker：
```bash
cd backend
celery -A app.core.celery_app worker --loglevel=info
```

4. 启动Celery Beat（定时任务）：
```bash
cd backend
celery -A app.core.celery_app beat --loglevel=info
```

5. 启动前端：
```bash
cd frontend
npm run dev
```

### 访问系统

- 前端界面: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs
- 默认登录账号: admin / admin123

## 使用指南

### 1. 智能体管理
- 访问"AI智能体"页面查看所有可用智能体
- 监控智能体健康状态
- 执行单个智能体或完整工作流程

### 2. 任务监控
- 实时查看任务执行状态和进度
- 支持任务取消和重试
- 查看详细的任务执行日志

### 3. 内容管理
- 管理新闻资讯源
- 查看和编辑爬取的内容
- 设置内容发布策略

### 4. 快速操作
在仪表板页面可以快速执行：
- 启动完整处理流程
- 爬取所有资讯源
- 处理新内容
- 自动发布内容

## 工作流程

### 完整处理流程
1. **新闻爬取**: 从配置的资讯源爬取最新新闻
2. **内容改写**: 使用AI将新闻改写为适合微信公众号的格式
3. **质量检查**: 多维度评估内容质量（准确性、可读性、原创性等）
4. **内容发布**: 根据策略自动发布到各个平台

### 自定义工作流程
- **仅爬取**: 只执行新闻爬取
- **仅处理**: 只对现有内容进行改写和质量检查
- **仅发布**: 只执行内容发布

## API文档

系统提供完整的RESTful API，支持：
- 智能体管理和执行
- 任务管理和监控
- 内容CRUD操作
- 用户认证和授权

详细API文档请访问: http://localhost:8000/docs

## 开发说明

### 项目结构
```
├── backend/                 # 后端代码
│   ├── app/
│   │   ├── agents/         # AI智能体
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── tasks/          # Celery任务
│   │   └── utils/          # 工具函数
│   └── requirements.txt
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── api/           # API客户端
│   │   ├── components/    # Vue组件
│   │   ├── views/         # 页面视图
│   │   ├── stores/        # Pinia状态管理
│   │   └── types/         # TypeScript类型
│   └── package.json
├── start_system.py        # 系统启动脚本
└── README.md
```

### 代码规范
- Python: 遵循PEP 8，使用4空格缩进
- TypeScript/Vue: 使用2空格缩进，遵循Vue 3 Composition API规范
- 提交信息: 使用约定式提交格式

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 确保Redis服务正在运行
   - 检查Redis配置和端口

2. **数据库连接失败**
   - 检查数据库服务状态
   - 验证连接配置信息

3. **前端无法访问后端API**
   - 检查后端服务是否启动
   - 确认API端口配置正确

4. **Celery任务不执行**
   - 检查Celery Worker是否启动
   - 查看Celery日志排查错误

### 日志查看
- 后端日志: 控制台输出
- Celery日志: 控制台输出
- 前端日志: 浏览器开发者工具

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发送邮件
- 项目讨论区

---

**注意**: 这是一个演示项目，生产环境使用前请进行充分的安全配置和性能优化。
