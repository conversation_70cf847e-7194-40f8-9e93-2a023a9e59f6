#!/usr/bin/env python3
"""
批量修复所有图标问题
"""

import os
import re
import subprocess

def find_vue_files():
    """找到所有Vue文件"""
    vue_files = []
    frontend_src = "frontend/src"
    
    for root, dirs, files in os.walk(frontend_src):
        for file in files:
            if file.endswith('.vue'):
                vue_files.append(os.path.join(root, file))
    
    return vue_files

def check_and_fix_icons():
    """检查并修复图标问题"""
    print("🔍 检查所有Vue文件中的图标问题...")
    
    # 问题图标映射
    icon_replacements = {
        'Robot': 'Cpu',
        'Play': 'Cpu', 
        'Pause': 'Monitor',
        'Stop': 'Monitor',
        'Record': 'Document',
        'VideoPlay': 'Document',
        'VideoPause': 'Document',
        'MusicPlay': 'Document',
        'MusicPause': 'Document',
        'Player': 'Monitor',
        'PlayerPlay': 'Monitor'
    }
    
    vue_files = find_vue_files()
    fixed_files = []
    
    for file_path in vue_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_modified = False
            
            # 修复导入语句
            for old_icon, new_icon in icon_replacements.items():
                # 修复导入中的图标
                import_pattern = r'(import\s*{[^}]*?)' + old_icon + r'([^}]*}\s*from\s*[\'"]@element-plus/icons-vue[\'"])'
                if re.search(import_pattern, content):
                    # 移除问题图标
                    content = re.sub(r',\s*' + old_icon + r'(?=\s*[,}])', '', content)
                    content = re.sub(r'(?<={\s*)' + old_icon + r'\s*,\s*', '', content)
                    content = re.sub(r'(?<=,\s*)' + old_icon + r'(?=\s*})', '', content)
                    file_modified = True
                    print(f"  📝 {file_path}: 移除导入 {old_icon}")
                
                # 修复模板中的使用
                template_pattern = f'<{old_icon}\\s*/>'
                if re.search(template_pattern, content):
                    content = re.sub(template_pattern, f'<{new_icon} />', content)
                    file_modified = True
                    print(f"  📝 {file_path}: 替换模板 {old_icon} → {new_icon}")
            
            # 如果文件被修改，写回文件
            if file_modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files.append(file_path)
                
        except Exception as e:
            print(f"⚠️ 处理文件 {file_path} 时出错: {str(e)}")
    
    return fixed_files

def test_build():
    """测试构建"""
    print("\n🚀 测试构建...")
    
    try:
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="frontend",
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=60
        )
        
        if result.returncode == 0:
            print("✅ 构建成功!")
            return True
        else:
            print("❌ 构建失败:")
            print(result.stderr)
            
            # 检查是否还有图标问题
            if "is not exported by" in result.stderr and "icons-vue" in result.stderr:
                icon_match = re.search(r'"([^"]+)" is not exported by.*icons-vue', result.stderr)
                if icon_match:
                    missing_icon = icon_match.group(1)
                    print(f"\n⚠️ 仍有图标问题: {missing_icon}")
                    return False
            
            return False
            
    except Exception as e:
        print(f"❌ 构建出错: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 图标修复工具")
    print("=" * 50)
    
    # 修复图标
    fixed_files = check_and_fix_icons()
    
    if fixed_files:
        print(f"\n✅ 修复了 {len(fixed_files)} 个文件:")
        for file_path in fixed_files:
            print(f"  📄 {file_path}")
    else:
        print("\n✅ 没有发现需要修复的图标问题")
    
    # 测试构建
    success = test_build()
    
    if success:
        print("\n🎉 所有图标问题已修复，构建成功!")
        print("现在可以运行: python start_full_system.py")
    else:
        print("\n⚠️ 可能还有其他问题需要手动修复")

if __name__ == "__main__":
    main()
