#!/usr/bin/env python3
"""
AI资讯智能体系统依赖安装脚本
分步安装依赖，处理可能的兼容性问题
"""

import os
import sys
import subprocess
from pathlib import Path

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent
BACKEND_DIR = PROJECT_ROOT / "backend"
FRONTEND_DIR = PROJECT_ROOT / "frontend"

def run_command(command, cwd=None, check=True):
    """运行命令并处理错误"""
    print(f"执行命令: {' '.join(command) if isinstance(command, list) else command}")
    try:
        result = subprocess.run(
            command, 
            cwd=cwd, 
            check=check, 
            capture_output=True, 
            text=True,
            shell=isinstance(command, str)
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        if check:
            return None
        return e

def install_python_dependencies():
    """安装Python依赖"""
    print("=" * 50)
    print("安装Python后端依赖")
    print("=" * 50)
    
    # 核心依赖组
    core_deps = [
        "fastapi>=0.100.0",
        "uvicorn[standard]>=0.23.0", 
        "pydantic>=2.0.0",
        "pydantic-settings>=2.0.0"
    ]
    
    database_deps = [
        "sqlalchemy>=2.0.0",
        "alembic>=1.12.0",
        "psycopg2-binary>=2.9.0",
        "pymongo>=4.5.0"
    ]
    
    async_deps = [
        "redis>=5.0.0",
        "celery>=5.3.0"
    ]
    
    ai_deps = [
        "transformers>=4.30.0",
        "torch>=2.0.0",
        "huggingface-hub>=0.16.0"
    ]
    
    http_deps = [
        "httpx>=0.24.0",
        "aiohttp>=3.8.0", 
        "requests>=2.31.0"
    ]
    
    scraping_deps = [
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0",
        "selenium>=4.15.0"
    ]
    
    utility_deps = [
        "python-multipart>=0.0.6",
        "aiofiles>=23.0.0",
        "python-jose[cryptography]>=3.3.0",
        "passlib[bcrypt]>=1.7.4",
        "python-dotenv>=1.0.0",
        "loguru>=0.7.0",
        "rich>=13.0.0",
        "click>=8.1.0",
        "python-dateutil>=2.8.0",
        "pytz>=2023.3"
    ]
    
    data_deps = [
        "pandas>=2.0.0",
        "numpy>=1.24.0"
    ]
    
    dev_deps = [
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "black>=23.0.0",
        "isort>=5.12.0"
    ]
    
    # 分组安装
    dep_groups = [
        ("核心Web框架", core_deps),
        ("数据库", database_deps), 
        ("异步和缓存", async_deps),
        ("HTTP客户端", http_deps),
        ("网页爬取", scraping_deps),
        ("工具库", utility_deps),
        ("数据处理", data_deps),
        ("开发工具", dev_deps),
        ("AI框架", ai_deps)  # AI依赖放在最后，因为可能比较大
    ]
    
    failed_packages = []
    
    for group_name, deps in dep_groups:
        print(f"\n安装 {group_name} 依赖...")
        for dep in deps:
            result = run_command([
                sys.executable, "-m", "pip", "install", dep
            ], cwd=BACKEND_DIR, check=False)
            
            if result is None or result.returncode != 0:
                print(f"⚠️  {dep} 安装失败，跳过...")
                failed_packages.append(dep)
            else:
                print(f"✓ {dep} 安装成功")
    
    # 尝试安装smolagents
    print(f"\n安装smolagents...")
    result = run_command([
        sys.executable, "-m", "pip", "install", "smolagents"
    ], cwd=BACKEND_DIR, check=False)
    
    if result is None or result.returncode != 0:
        print("⚠️  smolagents安装失败，尝试安装最新版本...")
        result = run_command([
            sys.executable, "-m", "pip", "install", "smolagents", "--upgrade"
        ], cwd=BACKEND_DIR, check=False)
        
        if result is None or result.returncode != 0:
            failed_packages.append("smolagents")
    
    if failed_packages:
        print(f"\n⚠️  以下包安装失败:")
        for pkg in failed_packages:
            print(f"   - {pkg}")
        print("\n您可以稍后手动安装这些包，或者使用不同的版本")
    else:
        print("\n✅ 所有Python依赖安装完成!")

def install_frontend_dependencies():
    """安装前端依赖"""
    print("\n" + "=" * 50)
    print("安装前端依赖")
    print("=" * 50)
    
    # 检查npm
    result = run_command(["npm", "--version"], check=False)
    if result is None or result.returncode != 0:
        print("❌ npm未安装，请先安装Node.js")
        return False
    
    # 安装依赖
    print("安装前端npm依赖...")
    result = run_command(["npm", "install"], cwd=FRONTEND_DIR, check=False)
    
    if result is None or result.returncode != 0:
        print("⚠️  npm install失败，尝试清理缓存后重试...")
        run_command(["npm", "cache", "clean", "--force"], cwd=FRONTEND_DIR, check=False)
        result = run_command(["npm", "install"], cwd=FRONTEND_DIR, check=False)
        
        if result is None or result.returncode != 0:
            print("❌ 前端依赖安装失败")
            return False
    
    print("✅ 前端依赖安装完成!")
    return True

def create_env_file():
    """创建环境配置文件"""
    print("\n" + "=" * 50)
    print("创建环境配置文件")
    print("=" * 50)
    
    env_file = BACKEND_DIR / ".env"
    env_example = BACKEND_DIR / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        import shutil
        shutil.copy2(env_example, env_file)
        print("✅ 环境配置文件已创建: backend/.env")
        print("⚠️  请编辑该文件配置数据库连接等信息")
    else:
        print("ℹ️  环境配置文件已存在")

def main():
    """主函数"""
    print("AI资讯智能体系统 - 依赖安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version < (3, 8):
        print(f"❌ Python版本过低: {python_version.major}.{python_version.minor}")
        print("需要Python 3.8或更高版本")
        sys.exit(1)
    
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 升级pip
    print("\n升级pip...")
    run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=False)
    
    # 安装Python依赖
    install_python_dependencies()
    
    # 安装前端依赖
    install_frontend_dependencies()
    
    # 创建环境配置文件
    create_env_file()
    
    print("\n" + "=" * 60)
    print("安装完成!")
    print("=" * 60)
    print("接下来的步骤:")
    print("1. 编辑 backend/.env 文件配置数据库连接")
    print("2. 启动Redis服务")
    print("3. 运行 python start_system.py 启动系统")
    print("=" * 60)

if __name__ == "__main__":
    main()
