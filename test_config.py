#!/usr/bin/env python3
"""
测试配置文件加载
"""

import os
import sys
from pathlib import Path

# 切换到backend目录
backend_dir = Path(__file__).parent / "backend"
os.chdir(backend_dir)
sys.path.insert(0, str(backend_dir))

try:
    from app.core.config import settings
    print("✅ 配置文件加载成功!")
    print(f"应用名称: {settings.app_name}")
    print(f"Redis主机: {settings.redis_host}")
    print(f"Redis端口: {settings.redis_port}")
    print(f"Redis用户名: {settings.redis_username}")
    print(f"环境: {getattr(settings, 'environment', 'development')}")
except Exception as e:
    print(f"❌ 配置文件加载失败: {e}")
    sys.exit(1)
