#!/usr/bin/env python3
"""
详细API测试
"""

import requests
import json

def test_api_detailed():
    """详细测试API"""
    print("🔍 详细API测试...")
    
    base_url = "http://127.0.0.1:8000"
    
    # 测试根路径
    try:
        print("1. 测试根路径 /")
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试健康检查
    try:
        print("\n2. 测试健康检查 /health")
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试API文档
    try:
        print("\n3. 测试API文档 /api/docs")
        response = requests.get(f"{base_url}/api/docs", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ API文档可访问")
        else:
            print(f"   错误: {response.text[:200]}...")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试OpenAPI规范
    try:
        print("\n4. 测试OpenAPI规范 /api/v1/openapi.json")
        response = requests.get(f"{base_url}/api/v1/openapi.json", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            openapi_data = response.json()
            print(f"   ✅ OpenAPI规范可访问")
            print(f"   路径数量: {len(openapi_data.get('paths', {}))}")
            
            # 显示可用路径
            paths = openapi_data.get('paths', {})
            if paths:
                print("   可用路径:")
                for path in sorted(paths.keys())[:10]:  # 只显示前10个
                    print(f"     {path}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试登录API
    try:
        print("\n5. 测试登录API /api/v1/auth/login")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(
            f"{base_url}/api/v1/auth/login",
            json=login_data,
            timeout=5
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 登录成功")
            print(f"   响应: {json.dumps(response.json(), indent=2, ensure_ascii=False)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")
    
    # 测试用户API
    try:
        print("\n6. 测试用户API /api/v1/users/")
        response = requests.get(f"{base_url}/api/v1/users/", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   ✅ 用户API正常")
            users = response.json()
            print(f"   用户数量: {len(users)}")
        else:
            print(f"   错误: {response.text}")
    except Exception as e:
        print(f"   异常: {str(e)}")

def test_frontend_login():
    """测试前端登录流程"""
    print("\n🔍 测试前端登录流程...")
    
    # 模拟前端登录请求
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        # 使用前端会使用的完整URL
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/login",
            json=login_data,
            headers={
                'Content-Type': 'application/json',
                'Origin': 'http://127.0.0.1:8000',
                'Referer': 'http://127.0.0.1:8000/'
            },
            timeout=5
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 前端登录流程正常")
            token_data = response.json()
            
            # 测试使用token访问受保护的API
            token = token_data.get('access_token')
            if token:
                print("\n测试token访问...")
                headers = {'Authorization': f'Bearer {token}'}
                me_response = requests.get(
                    "http://127.0.0.1:8000/api/v1/auth/me",
                    headers=headers,
                    timeout=5
                )
                print(f"用户信息API状态码: {me_response.status_code}")
                if me_response.status_code == 200:
                    print("✅ Token验证正常")
                else:
                    print("⚠️ Token验证有问题")
        else:
            print(f"❌ 前端登录失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 前端登录异常: {str(e)}")

def main():
    """主函数"""
    print("=" * 60)
    print("AI资讯智能体系统 - 详细API测试")
    print("=" * 60)
    
    test_api_detailed()
    test_frontend_login()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("如果登录API正常，请在浏览器访问: http://127.0.0.1:8000")

if __name__ == "__main__":
    main()
