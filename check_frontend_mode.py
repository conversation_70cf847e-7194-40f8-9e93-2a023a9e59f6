#!/usr/bin/env python3
"""
检查前端运行模式
"""

import requests
import subprocess
import os

def check_frontend_mode():
    """检查前端运行模式"""
    print("🔍 检查前端运行模式...")
    
    # 检查开发服务器（5173端口）
    try:
        print("1. 检查开发服务器 (localhost:5173)...")
        response = requests.get("http://localhost:5173", timeout=3)
        if response.status_code == 200:
            print("   ✅ 开发服务器运行中")
            print("   📍 前端地址: http://localhost:5173")
            return "development", "http://localhost:5173"
        else:
            print("   ❌ 开发服务器无响应")
    except Exception as e:
        print(f"   ❌ 开发服务器连接失败: {str(e)}")
    
    # 检查生产模式（通过后端8000端口）
    try:
        print("2. 检查生产模式 (localhost:8000)...")
        response = requests.get("http://localhost:8000", timeout=3)
        if response.status_code == 200:
            print("   ✅ 后端服务运行中")
            
            # 检查是否有前端静态文件
            if os.path.exists("frontend/dist"):
                print("   ✅ 前端构建文件存在")
                print("   📍 前端地址: http://localhost:8000")
                return "production", "http://localhost:8000"
            else:
                print("   ❌ 前端构建文件不存在")
                return "backend_only", "http://localhost:8000"
        else:
            print("   ❌ 后端服务无响应")
    except Exception as e:
        print(f"   ❌ 后端服务连接失败: {str(e)}")
    
    return "none", None

def test_api_from_frontend(frontend_url):
    """从前端角度测试API"""
    print(f"\n🔍 从前端角度测试API ({frontend_url})...")
    
    if "5173" in frontend_url:
        # 开发模式：前端会代理API请求到后端
        api_base = frontend_url + "/api/v1"
        print("   模式: 开发模式 (Vite代理)")
    else:
        # 生产模式：直接访问后端API
        api_base = frontend_url + "/api/v1"
        print("   模式: 生产模式 (直接访问)")
    
    # 测试登录API
    try:
        print("   测试登录API...")
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        response = requests.post(
            f"{api_base}/auth/login",
            json=login_data,
            timeout=5
        )
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ 登录API正常")
            return True
        else:
            print("   ❌ 登录API失败")
            print(f"   错误: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 登录API错误: {str(e)}")
        return False

def get_running_processes():
    """获取运行中的相关进程"""
    print("\n🔍 检查相关进程...")
    
    try:
        # 检查Node.js进程（前端开发服务器）
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq node.exe"],
            capture_output=True,
            text=True,
            shell=True
        )
        if "node.exe" in result.stdout:
            print("   ✅ Node.js进程运行中 (可能是前端开发服务器)")
        else:
            print("   ❌ 未发现Node.js进程")
    except:
        print("   ⚠️ 无法检查Node.js进程")
    
    try:
        # 检查Python进程（后端服务器）
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq python.exe"],
            capture_output=True,
            text=True,
            shell=True
        )
        if "python.exe" in result.stdout:
            print("   ✅ Python进程运行中 (可能是后端服务器)")
        else:
            print("   ❌ 未发现Python进程")
    except:
        print("   ⚠️ 无法检查Python进程")

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 前端模式检查")
    print("=" * 50)
    
    # 检查进程
    get_running_processes()
    
    # 检查前端模式
    mode, frontend_url = check_frontend_mode()
    
    if mode == "development":
        print("\n✅ 检测到开发模式")
        print("前端通过Vite开发服务器运行，API请求会被代理到后端")
        if test_api_from_frontend(frontend_url):
            print("🎉 前端API连接正常！")
        else:
            print("❌ 前端API连接有问题")
    elif mode == "production":
        print("\n✅ 检测到生产模式")
        print("前端通过后端静态文件服务运行")
        if test_api_from_frontend(frontend_url):
            print("🎉 前端API连接正常！")
        else:
            print("❌ 前端API连接有问题")
    elif mode == "backend_only":
        print("\n⚠️ 仅后端运行")
        print("需要构建前端或启动开发服务器")
        print("运行: cd frontend && npm run dev")
    else:
        print("\n❌ 未检测到运行中的服务")
        print("请启动后端和前端服务")

if __name__ == "__main__":
    main()
