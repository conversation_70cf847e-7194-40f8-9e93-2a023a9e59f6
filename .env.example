# 应用配置
APP_NAME=AI资讯智能体
APP_VERSION=1.0.0
DEBUG=false

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password
POSTGRES_DB=ai_news_agent

# MongoDB配置
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB=ai_news_content

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# JWT配置
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI模型配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# 爬虫配置
USER_AGENT=AI-News-Agent/1.0
REQUEST_DELAY=1.0
MAX_RETRIES=3
