#!/usr/bin/env python3
"""
修复后端路由问题
"""

import os
import sys

def create_minimal_working_backend():
    """创建一个最小可工作的后端"""
    print("🔧 创建最小可工作的后端...")
    
    # 创建临时的main.py
    minimal_main = '''"""
最小可工作的FastAPI后端
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import os

# 创建FastAPI应用
app = FastAPI(
    title="AI资讯智能体API",
    description="AI驱动的资讯聚合与智能改写系统",
    version="1.0.0",
    openapi_url="/api/v1/openapi.json",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_id: str
    username: str

class UserInfo(BaseModel):
    user_id: str
    username: str
    email: str = "<EMAIL>"
    is_active: bool = True

# 根路径
@app.get("/")
async def root():
    return {
        "message": "AI资讯智能体API",
        "app_name": "AI资讯智能体",
        "version": "1.0.0",
        "docs_url": "/api/docs",
        "api_url": "/api/v1",
        "status": "running"
    }

# 健康检查
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "app_name": "AI资讯智能体",
        "version": "1.0.0"
    }

# 登录API
@app.post("/api/v1/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """用户登录"""
    if request.username == "admin" and request.password == "admin123":
        return LoginResponse(
            access_token="demo_token_12345",
            user_id="user_001",
            username=request.username
        )
    raise HTTPException(status_code=401, detail="用户名或密码错误")

# 登出API
@app.post("/api/v1/auth/logout")
async def logout():
    """用户登出"""
    return {"message": "登出成功"}

# 获取用户信息
@app.get("/api/v1/auth/me", response_model=UserInfo)
async def get_current_user():
    """获取当前用户信息"""
    return UserInfo(
        user_id="user_001",
        username="admin",
        email="<EMAIL>"
    )

# 用户列表API
@app.get("/api/v1/users/")
async def get_users():
    """获取用户列表"""
    return [
        {
            "id": "user_001",
            "username": "admin",
            "email": "<EMAIL>",
            "is_active": True
        }
    ]

# 静态文件服务
if os.path.exists("../frontend/dist"):
    app.mount("/", StaticFiles(directory="../frontend/dist", html=True), name="frontend")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main_minimal:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
'''
    
    # 写入文件
    with open("backend/main_minimal.py", "w", encoding="utf-8") as f:
        f.write(minimal_main)
    
    print("✅ 创建了最小后端: backend/main_minimal.py")
    return True

def start_minimal_backend():
    """启动最小后端"""
    print("🚀 启动最小后端...")

    import subprocess

    try:
        # 使用uvicorn命令启动，指定正确的模块路径
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main_minimal:app",
            "--host", "127.0.0.1",
            "--port", "8000",
            "--reload"
        ]

        print(f"执行命令: {' '.join(cmd)}")
        print("在backend目录中启动...")

        process = subprocess.Popen(
            cmd,
            cwd="backend",
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True
        )

        print("✅ 最小后端启动中...")
        print("📍 访问地址: http://127.0.0.1:8000")
        print("📍 API文档: http://127.0.0.1:8000/api/docs")
        print("\n按 Ctrl+C 停止服务")

        # 显示输出
        try:
            for line in process.stdout:
                print(line.strip())
        except KeyboardInterrupt:
            print("\n正在停止服务...")
            process.terminate()
            process.wait()
            print("✅ 服务已停止")

    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")

def main():
    """主函数"""
    print("=" * 50)
    print("AI资讯智能体系统 - 后端路由修复")
    print("=" * 50)
    
    if not os.path.exists("backend"):
        print("❌ 后端目录不存在")
        return
    
    # 创建最小后端
    if create_minimal_working_backend():
        print("\n选择操作:")
        print("1. 启动最小后端 (推荐)")
        print("2. 仅创建文件")
        
        choice = input("\n请选择 (1-2): ").strip()
        
        if choice == "1":
            start_minimal_backend()
        else:
            print("\n✅ 最小后端文件已创建")
            print("手动启动: cd backend && python main_minimal.py")

if __name__ == "__main__":
    main()
