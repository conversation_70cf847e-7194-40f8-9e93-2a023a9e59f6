# AI资讯智能体后端依赖

# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
smolagents==0.3.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
motor==3.3.2
redis==5.0.1

# 任务队列
celery==5.3.4
celery[redis]==5.3.4

# 数据处理
pandas==2.1.3
beautifulsoup4==4.12.2
scrapy==2.11.0
nltk==3.8.1
spacy==3.7.2
requests==2.31.0
aiohttp==3.9.1

# AI和NLP
openai==1.3.7
transformers==4.35.2
torch==2.1.1

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 配置和环境
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 实时通信
python-socketio==5.10.0

# 文件处理
python-magic==0.4.27
pillow==10.1.0

# 工具库
httpx==0.25.2
tenacity==8.2.3
schedule==1.2.0
