#!/usr/bin/env python3
"""
检查前端文件中可能有问题的图标
"""

import os
import re

def check_icons():
    """检查图标使用"""
    print("检查前端文件中的图标使用...")
    
    # 常见的可能不存在的图标
    problematic_icons = [
        'Robot', 'Play', 'Pause', 'Stop', 'Record', 'VideoPlay',
        'VideoPause', 'MusicPlay', 'MusicPause', 'Player', 'PlayerPlay'
    ]
    
    # 扫描的文件类型
    file_extensions = ['.vue', '.ts', '.js']
    
    issues_found = []
    
    # 扫描前端目录
    frontend_dir = "frontend/src"
    if not os.path.exists(frontend_dir):
        print("❌ 前端源码目录不存在")
        return
    
    for root, dirs, files in os.walk(frontend_dir):
        for file in files:
            if any(file.endswith(ext) for ext in file_extensions):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                        # 检查导入语句中的问题图标
                        import_pattern = r'import\s*{[^}]*}\s*from\s*[\'"]@element-plus/icons-vue[\'"]'
                        import_matches = re.findall(import_pattern, content, re.MULTILINE | re.DOTALL)
                        
                        for match in import_matches:
                            for icon in problematic_icons:
                                if icon in match:
                                    issues_found.append({
                                        'file': file_path,
                                        'type': 'import',
                                        'icon': icon,
                                        'line': content[:content.find(match)].count('\n') + 1
                                    })
                        
                        # 检查模板中的图标使用
                        for icon in problematic_icons:
                            icon_pattern = f'<{icon}\\s*/>'
                            if re.search(icon_pattern, content):
                                line_num = content[:content.find(f'<{icon}')].count('\n') + 1
                                issues_found.append({
                                    'file': file_path,
                                    'type': 'usage',
                                    'icon': icon,
                                    'line': line_num
                                })
                
                except Exception as e:
                    print(f"⚠️ 无法读取文件 {file_path}: {str(e)}")
    
    # 报告结果
    if issues_found:
        print(f"\n❌ 发现 {len(issues_found)} 个潜在图标问题:")
        for issue in issues_found:
            print(f"  📄 {issue['file']}:{issue['line']} - {issue['type']} '{issue['icon']}'")
        
        print("\n建议的替换方案:")
        replacements = {
            'Robot': 'Cpu',
            'Play': 'Cpu',
            'Pause': 'Monitor',
            'Stop': 'Monitor',
            'Record': 'Document',
            'VideoPlay': 'Document',
            'VideoPause': 'Document',
            'MusicPlay': 'Document',
            'MusicPause': 'Document',
            'Player': 'Monitor',
            'PlayerPlay': 'Monitor'
        }
        
        for old_icon, new_icon in replacements.items():
            if any(issue['icon'] == old_icon for issue in issues_found):
                print(f"  {old_icon} → {new_icon}")
        
        return False
    else:
        print("✅ 没有发现图标问题")
        return True

def list_available_icons():
    """列出一些常用的可用图标"""
    print("\n📋 常用的可用图标:")
    available_icons = [
        'Document', 'Link', 'Share', 'Cpu', 'Monitor', 'List', 'Setting',
        'User', 'ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight',
        'Download', 'Upload', 'Edit', 'Delete', 'Plus', 'Minus',
        'Search', 'Refresh', 'Close', 'Check', 'Warning', 'Info',
        'Success', 'Error', 'Loading', 'More', 'MoreFilled'
    ]
    
    for i, icon in enumerate(available_icons):
        if i % 4 == 0:
            print()
        print(f"  {icon:<15}", end="")
    print()

if __name__ == "__main__":
    success = check_icons()
    list_available_icons()
    
    if success:
        print("\n🎉 图标检查通过，可以尝试构建!")
    else:
        print("\n⚠️ 请修复上述图标问题后再构建")
