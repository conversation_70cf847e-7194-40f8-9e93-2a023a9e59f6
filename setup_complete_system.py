#!/usr/bin/env python3
"""
完整系统设置
"""

import os
import subprocess
import sys
import time

def build_frontend():
    """构建前端"""
    print("🔧 构建前端...")
    
    if not os.path.exists("frontend"):
        print("❌ 前端目录不存在")
        return False
    
    try:
        # 检查node_modules
        if not os.path.exists("frontend/node_modules"):
            print("📦 安装前端依赖...")
            result = subprocess.run(
                ["npm", "install"],
                cwd="frontend",
                capture_output=True,
                text=True
            )
            if result.returncode != 0:
                print(f"❌ 依赖安装失败: {result.stderr}")
                return False
            print("✅ 前端依赖安装完成")
        
        # 构建前端
        print("🏗️ 构建前端...")
        result = subprocess.run(
            ["npm", "run", "build"],
            cwd="frontend",
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ 前端构建成功")
            
            # 检查构建结果
            if os.path.exists("frontend/dist/index.html"):
                print("✅ 构建文件完整")
                return True
            else:
                print("❌ 构建文件不完整")
                return False
        else:
            print("❌ 前端构建失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建前端时出错: {str(e)}")
        return False

def restart_backend():
    """重启后端服务"""
    print("🔄 重启后端服务...")
    
    try:
        # 停止现有进程（温和方式）
        try:
            import psutil
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if 'uvicorn' in ' '.join(proc.info['cmdline'] or []):
                    print(f"停止进程: {proc.info['pid']}")
                    proc.terminate()
                    proc.wait(timeout=3)
        except:
            print("⚠️ 无法优雅停止进程，继续启动...")
        
        time.sleep(2)
        
        # 启动新的后端
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main_minimal:app",
            "--host", "127.0.0.1",
            "--port", "8001",
            "--reload"
        ]
        
        print(f"启动命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            cwd="backend"
        )
        
        print("✅ 后端重启中...")
        time.sleep(3)
        
        return True
        
    except Exception as e:
        print(f"❌ 重启后端失败: {str(e)}")
        return False

def test_complete_system():
    """测试完整系统"""
    print("🔍 测试完整系统...")
    
    import requests
    
    base_url = "http://127.0.0.1:8001"
    
    # 测试API
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ 后端API正常")
        else:
            print(f"   ❌ 后端API异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 后端API连接失败: {str(e)}")
        return False
    
    # 测试前端页面
    try:
        response = requests.get(f"{base_url}/app", timeout=5)
        if response.status_code == 200:
            print("   ✅ 前端页面可访问")
            return True
        else:
            print(f"   ❌ 前端页面异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 前端页面连接失败: {str(e)}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("📝 创建启动脚本...")
    
    startup_script = '''@echo off
echo 启动AI资讯智能体系统...
cd /d "%~dp0"
cd backend
python -m uvicorn main_minimal:app --host 127.0.0.1 --port 8001 --reload
pause
'''
    
    try:
        with open("start_system.bat", "w", encoding="utf-8") as f:
            f.write(startup_script)
        print("✅ 启动脚本已创建: start_system.bat")
        return True
    except Exception as e:
        print(f"❌ 创建启动脚本失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("AI资讯智能体系统 - 完整系统设置")
    print("=" * 60)
    
    success_steps = []
    
    # 步骤1: 构建前端
    if build_frontend():
        success_steps.append("前端构建")
    
    # 步骤2: 重启后端
    if restart_backend():
        success_steps.append("后端启动")
    
    # 步骤3: 测试系统
    if test_complete_system():
        success_steps.append("系统测试")
    
    # 步骤4: 创建启动脚本
    if create_startup_script():
        success_steps.append("启动脚本")
    
    print("\n" + "=" * 60)
    print("设置完成!")
    print(f"成功步骤: {', '.join(success_steps)}")
    
    if len(success_steps) >= 3:
        print("\n🎉 系统设置成功!")
        print("📍 访问地址:")
        print("   前端页面: http://127.0.0.1:8001/app")
        print("   API文档: http://127.0.0.1:8001/api/docs")
        print("   健康检查: http://127.0.0.1:8001/health")
        print("📍 登录账号: admin / admin123")
        print("\n💡 下次启动可以直接运行: start_system.bat")
    else:
        print("\n⚠️ 系统设置不完整，请检查错误信息")

if __name__ == "__main__":
    main()
